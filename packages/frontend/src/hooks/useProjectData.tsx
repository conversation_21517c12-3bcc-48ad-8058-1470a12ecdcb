import { useState, useEffect, useCallback } from 'react';
import apiClient from '@/lib/apiClient';
import { Project, ProjectImage, ImageStatus } from '@/types';
import { getProjectImages } from '@/api/projectImages';
import logger from '@/utils/logger';

export const useProjectData = (projectId: string | undefined) => {
  const [project, setProject] = useState<Project | null>(null);
  const [images, setImages] = useState<ProjectImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProjectAndImages = useCallback(async () => {
    if (!projectId) {
      setError('Project ID is undefined.');
      setLoading(false);
      return;
    }

    // Clean project ID for consistent API calls
    const cleanProjectId = projectId.startsWith('project-') ? projectId.substring(8) : projectId;
    logger.debug('Cleaning project ID:', cleanProjectId);

    try {
      setLoading(true);
      setError(null);

      // Fetch project details
      logger.debug('Starting API call to fetch project with ID:', cleanProjectId);
      const projectResponse = await apiClient.get(`/api/projects/${cleanProjectId}`);
      logger.debug('Successfully retrieved project data from API endpoint', projectResponse.data);
      setProject(projectResponse.data);

      // Fetch images for the project
      logger.debug('Fetching images for project:', cleanProjectId);
      const projectImages = await getProjectImages(cleanProjectId);
      logger.debug('Retrieved', projectImages.length, 'images for project');
      setImages(projectImages);
    } catch (err: any) { // Cast err to any
      logger.error('Error fetching project data:', err);
      setError(err.message || 'Failed to fetch project data.');
      setImages([]); // Clear images on error
    } finally {
      setLoading(false);
    }
  }, [projectId]);

  useEffect(() => {
    fetchProjectAndImages();
  }, [fetchProjectAndImages]);

  const updateImageStatus = useCallback((imageId: string, status: ImageStatus, resultPath?: string | null, error?: string) => {
    setImages((prevImages) =>
      prevImages.map((image) =>
        image.id === imageId
          ? { ...image, segmentationStatus: status, segmentationResultPath: resultPath, error: error }
          : image,
      ),
    );
    logger.debug(`Image status updated for ${imageId}: ${status}`);
  }, []);

  return {
    project,
    projectTitle: project?.title || 'Loading Project...',
    images,
    loading,
    error,
    refreshData: fetchProjectAndImages,
    updateImageStatus,
    setImages,
  };
};
