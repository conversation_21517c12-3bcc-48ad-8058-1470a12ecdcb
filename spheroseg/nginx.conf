server {
    listen 80;
    server_name spherosegapp.utia.cas.cz localhost;

    # Let's Encrypt verification
    location /.well-known/acme-challenge/ {
        root /var/www/letsencrypt;
        try_files $uri =404;
    }

    # Redirect HTTP to HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name spherosegapp.utia.cas.cz localhost;

    # SSL configuration - Let's Encrypt
    ssl_certificate /etc/letsencrypt/live/spherosegapp.utia.cas.cz-0001/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/spherosegapp.utia.cas.cz-0001/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;

    # Improve SSL security
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:10m;
    ssl_session_tickets off;

    # Add security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-XSS-Protection "1; mode=block";

    # HSTS (enabled with valid certificate)
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;

    # Set proper MIME types
    types {
        application/javascript js;
        application/javascript mjs; 
        application/javascript ts;
    }
    
    # Handle translation files including those with country codes (e.g., cs-CZ)
    # location ~ ^/src/translations/[a-z][a-z](-[A-Z][A-Z])?\.ts$ {
    #     add_header Content-Type "application/javascript; charset=utf-8";
    #     add_header Cache-Control "no-cache";
    #     add_header X-Content-Type-Options "nosniff";
    #     add_header Access-Control-Allow-Origin "*";
    #     return 200 "export default {};\n// Translation file placeholder";
    # }

    # Frontend proxy
    location / {
        proxy_pass http://spheroseg-frontend-dev:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # Disable caching for HTML to ensure fresh content
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";

        # Timeouts - increase to allow for longer loading times
        proxy_connect_timeout 120s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;

        # Buffer settings
        proxy_buffering off;
    }

    # Backend API proxy
    location /api {
        proxy_pass http://spheroseg-backend:5001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # Increase timeouts for API calls
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;

        # Increase buffer size for file uploads
        client_max_body_size 100M;
        proxy_request_buffering off;
        proxy_buffering off;
    }

    # Specific API endpoints for segmentations
    location /api/segmentations {
        proxy_pass http://spheroseg-backend:5001/api/segmentations;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Increase timeouts for segmentation API calls
        proxy_connect_timeout 600s;
        proxy_send_timeout 600s;
        proxy_read_timeout 600s;

        # Increase buffer size for file uploads
        client_max_body_size 200M;
        proxy_request_buffering off;
        proxy_buffering off;
    }

    # Specific API endpoints for projects
    location /api/projects {
        proxy_pass http://spheroseg-backend:5001/api/projects;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Increase timeouts for project API calls
        proxy_connect_timeout 600s;
        proxy_send_timeout 600s;
        proxy_read_timeout 600s;

        # Increase buffer size for file uploads
        client_max_body_size 200M;
        proxy_request_buffering off;
        proxy_buffering off;
    }

    # WebSocket proxy for HMR (Hot Module Replacement) - properly configured for SSL
    location /@hmr {
        proxy_pass http://spheroseg-frontend-dev:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # Assets proxy
    location /assets/ {
        proxy_pass http://spheroseg-assets:80/assets/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_cache_bypass $http_upgrade;
        add_header Cache-Control "public, max-age=3600";
    }

    # Uploads handling
    location /uploads/ {
        proxy_pass http://spheroseg-backend:5001/uploads/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Increase buffer size for file uploads
        client_max_body_size 100M;
        proxy_request_buffering off;
        proxy_buffering off;
    }
}
