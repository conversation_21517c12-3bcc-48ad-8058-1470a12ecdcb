# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
.pnpm-debug.log

# Build outputs
dist/
build/
.turbo/
.next/
out/
.nuxt/
.output/
.cache/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editors
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Testing
coverage/
.nyc_output/
cypress/videos/
cypress/screenshots/

# ML model weights
*.pth
*.pth.tar
*.pt
*.onnx
*.h5
*.hdf5
*.pb
*.tflite
*.mlmodel
*.caffemodel
*.weights
*.bin
checkpoint_*/

# Database
*.sqlite
*.sqlite3
*.db

# Uploads and media
uploads/
media/
public/uploads/
public/media/

# Temporary files
tmp/
temp/
.tmp/
.temp/
*.tmp
*.temp
.cache/
cache/

# Processing files
processing/
.processing/
segmentation_temp/
ml_temp/

# Docker
.docker/
docker-volumes/

# Package manager files
package-lock.json.bak
yarn.lock.bak
.pnpm-store/
.yarn/
.npm/

# Runtime files
*.pid
*.sock
*.lock

# Backup files
*.bak
*.backup
*~
*.orig

# Cache files
.cache/
.parcel-cache/
.webpack/
.eslintcache
.stylelintcache

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

**/.claude/settings.local.json

# SSL Certificates
letsencrypt/

# User data and uploads (prevent accidental commits)
user_uploads/
public/user_data/
assets/user_content/
storage/uploads/

# Generated reports
reports/
benchmarks/
performance_logs/

# IDE temp files
.vscode/settings.json
.idea/workspace.xml
.idea/tasks.xml
