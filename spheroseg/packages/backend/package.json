{"name": "@spheroseg/backend", "version": "1.0.0", "license": "MIT", "main": "dist/server.js", "scripts": {"build": "tsc && tsc-alias", "start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only --no-notify --no-deps --ignore-watch node_modules -r tsconfig-paths/register src/server.ts", "test": "jest", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "format": "prettier --write \"src/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\"", "migrate:access-requests": "node src/scripts/run-migration.js"}, "dependencies": {"@spheroseg/types": "file:../types", "@types/compression": "^1.8.1", "@types/morgan": "^1.9.10", "@types/node-fetch": "^2.6.12", "@types/nodemailer": "^6.4.17", "@types/uuid": "^10.0.0", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.17.1", "express-rate-limit": "^7.5.1", "helmet": "^7.2.0", "jsonwebtoken": "^9.0.0", "jszip": "^3.10.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "node-cron": "^3.0.3", "node-fetch": "^2.7.0", "nodemailer": "^7.0.3", "pg": "^8.15.6", "sharp": "^0.34.1", "socket.io": "^4.7.5", "ts-node": "^10.9.2", "uuid": "^11.1.0", "winston": "^3.17.0", "zod": "^3.24.3"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/express": "^4.17.1", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/node": "^18.11.9", "@types/node-cron": "^3.0.11", "@types/pg": "^8.11.10", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "eslint": "^8.57.1", "jest": "^29.7.0", "prettier": "^3.2.5", "rimraf": "^5.0.5", "supertest": "^7.1.0", "ts-jest": "^29.3.2", "ts-node-dev": "^2.0.0", "tsc-alias": "^1.8.10", "tsconfig-paths": "^4.2.0", "typescript": "^5.0.0"}, "keywords": [], "author": "", "types": "./dist/server.d.ts", "description": ""}