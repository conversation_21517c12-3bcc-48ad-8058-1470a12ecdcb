{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["es2020"], "outDir": "dist", "rootDir": "src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@spheroseg/types": ["../types/src"]}, "resolveJsonModule": true, "declaration": true, "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}