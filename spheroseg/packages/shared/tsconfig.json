{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "esModuleInterop": true, "declaration": true, "outDir": "dist", "strict": true, "jsx": "react-jsx", "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "baseUrl": ".", "paths": {"@shared/*": ["./*"]}}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "dist"]}