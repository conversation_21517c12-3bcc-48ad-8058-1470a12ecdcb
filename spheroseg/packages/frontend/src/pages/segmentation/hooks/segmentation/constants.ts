// --- Constants ---
export const MIN_ZOOM = 0.5; // Changed from 0.1 to 0.5 (50%)
export const MAX_ZOOM = 10.0; // 1000%

// Minimum distance for adding a new point in equidistant mode
export const MIN_EQUIDISTANT_DISTANCE = 20;

// Vertex hit radius for selection
export const VERTEX_HIT_RADIUS = 10; // Smaller hit radius for more precise selection

// Close distance for polygon creation
export const CLOSE_POLYGON_DISTANCE = 20;

// Debounce delay for fetching data
export const FETCH_DEBOUNCE_DELAY = 500; // ms
