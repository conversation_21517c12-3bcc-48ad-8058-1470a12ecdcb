export default {
  // Segmentation context menu
  segmentation: {
    contextMenu: {
      editPolygon: '编辑多边形',
      splitPolygon: '分割多边形',
      deletePolygon: '删除多边形',
      confirmDeleteTitle: '您确定要删除多边形吗？',
      confirmDeleteMessage: '此操作不可逆。多边形将从分割中永久删除。',
      duplicateVertex: '复制顶点',
      deleteVertex: '删除顶点',
    },
  },
  // Common UI elements  
  common: {
    cancel: '取消',
    delete: '删除',
    loadingApplication: '正在加载应用程序...',
    selectAll: '全选',
  },
  // Error messages
  errors: {
    somethingWentWrong: '出现了问题',
    componentError: '此组件发生错误。我们已收到通知，将尽快解决问题。',
    errorDetails: '错误详细信息',
    tryAgain: '重试',
    reloadPage: '重新加载页面',
    goBack: '返回',
    notFound: '404',
    pageNotFoundMessage: '糟糕！页面未找到',
    returnToHome: '返回首页',
  },
  project: {
    detail: {
      noImagesSelected: '未选择图像',
      triggeringResegmentation: '正在为 {{count}} 张图像触发重新分割...',
      deleteConfirmation: '您确定要删除 {{count}} 张图像吗？此操作无法撤消。',
      deletingImages: '正在删除 {{count}} 张图像...',
      deleteSuccess: '成功删除 {{count}} 张图像',
      deleteFailed: '删除 {{count}} 张图像失败',
      preparingExport: '正在准备导出 {{count}} 张图像...'
    },
    segmentation: {
      processingInBatches: '开始分批处理 {{count}} 张图像，共 {{batches}} 批...',
      batchQueued: '批次 {{current}}/{{total}} 已成功排队',
      batchQueuedFallback: '批次 {{current}}/{{total}} 已成功排队（备用端点）',
      batchError: '处理批次 {{current}}/{{total}} 时出错',
      partialSuccess: '分割：{{success}} 张图像成功排队，{{failed}} 张失败',
      allSuccess: '分割：所有 {{count}} 张图像成功排队',
      allFailed: '分割：所有 {{count}} 张图像均失败',
      startedImages: '已为 {{count}} 张图像启动分割',
      queuedLocallyWarning: '已在本地为 {{count}} 张图像排队分割。服务器连接失败。'
    },
    noImages: {
      title: '暂无图像',
      description: '此项目尚无图像。上传图像以开始分割。',
      uploadButton: '上传图像',
    },
  },
  common: {
    appName: '类器官分割平台',
    appNameShort: 'SpheroSeg',
    loading: '加载中...',
    save: '保存',
    cancel: '取消',
    delete: '删除',
    edit: '编辑',
    create: '创建',
    search: '搜索',
    error: '错误',
    success: '成功',
    back: '返回',
    signIn: '登录',
    signUp: '注册',
    signOut: '退出登录',
    settings: '设置',
    profile: '个人资料',
    dashboard: '仪表板',
    project: '项目',
    projects: '项目',
    newProject: '新项目',
    upload: '上传',
    uploadImages: '上传图片',
    recentAnalyses: '最近分析',
    noProjects: '未找到项目',
    noImages: '未找到图片',
    createYourFirst: '创建您的第一个项目以开始',
    tryAgain: '重试',
    email: '电子邮件',
    password: '密码',
    name: '名称',
    description: '描述',
    date: '日期',
    status: '状态',
    images: '图片',
    image: '图片',
    projectName: '项目名称',
    projectDescription: '项目描述',
    theme: '主题',
    language: '语言',
    light: '浅色',
    dark: '深色',
    system: '系统',
    welcome: '欢迎使用类器官分割平台',
    account: '账户',
    passwordConfirm: '确认密码',
    manageAccount: '管理您的账户',
    changePassword: '更改密码',
    deleteAccount: '删除账户',
    requestAccess: '请求访问',
    termsOfService: '服务条款',
    privacyPolicy: '隐私政策',
    accessRequest: '访问请求',
    createAccount: '创建账户',
    signInToAccount: '登录您的账户',
    sort: '排序',
    backToHome: '返回首页',
    termsOfServiceLink: '服务条款',
    privacyPolicyLink: '隐私政策',
    optional: '可选',
    saveChanges: '保存更改',
    saving: '保存中',
    notSpecified: '未指定',
    no: '否',
    profileTitle: '个人资料',
    profileDescription: '更新您的个人资料信息，该信息对其他用户可见',
    profileUsername: '用户名',
    profileUsernamePlaceholder: '输入您的用户名',
    profileFullName: '全名',
    profileFullNamePlaceholder: '输入您的全名',
    profileTitlePlaceholder: '例如：研究科学家，教授',
    profileOrganization: '组织',
    profileOrganizationPlaceholder: '输入您的组织或机构',
    profileBio: '个人简介',
    profileBioPlaceholder: '写一个关于您自己的简短介绍',
    profileBioDescription: '简要描述您的研究兴趣和专长',
    profileLocation: '位置',
    profileLocationPlaceholder: '例如：北京，中国',
    profileSaveButton: '保存资料',
    noPreview: '无预览',
    themeDescription: '选择您喜欢的主题',
    languageDescription: '选择您喜欢的语言',
    reset: '重置',
    clear: '清除',
    download: '下载',
    removeAll: '全部删除',
    confirmPassword: '确认密码',
    firstName: '名字',
    lastName: '姓氏',
    username: '用户名',
    enable: '启用',
    disable: '禁用',
    and: '和',
    lastChange: '最后更改',
    emailPlaceholder: '输入您的电子邮件',
    passwordPlaceholder: '输入您的密码',
    export: '导出',
    selectImages: '选择图片',
    noImagesDescription: '上传图片以开始您的项目',
    yes: '是',
  },
  requestAccess: {
    title: '请求访问类器官分割平台',
    description: '填写以下表格以请求访问我们的平台。我们将审核您的请求并尽快回复您。',
    emailLabel: '您的电子邮件地址',
    nameLabel: '您的姓名',
    institutionLabel: '机构/公司',
    reasonLabel: '访问原因',
    submitRequest: '提交请求',
    requestReceived: '已收到请求',
    thankYou: '感谢您的关注',
    weWillContact: '我们将审核您的请求并尽快与您联系',
    submitSuccess: '请求提交成功！',
    and: '和',
  },
  documentation: {
    tag: '用户指南',
    title: 'SpheroSeg 文档',
    subtitle: '学习如何有效地使用类器官分割平台。',
    sidebar: {
      title: '章节',
      introduction: '介绍',
      gettingStarted: '入门',
      uploadingImages: '上传图像',
      segmentationProcess: '分割过程',
      apiReference: 'API 参考',
    },
    introduction: {
      title: '介绍',
      imageAlt: '类器官分析工作流程图示',
      whatIs: {
        title: '什么是 SpheroSeg？',
        paragraph1: 'SpheroSeg 是一个专为显微镜图像中的细胞类器官分析和分割设计的前沿平台。',
        paragraph2: '它利用先进的人工智能算法，以高精度自动检测和分析类器官的边界。',
        paragraph3: '本文档将指导您了解平台上所有可用的功能和工具。',
      },
    },
    gettingStarted: {
      title: '入门',
      accountCreation: {
        title: '创建账户',
        paragraph1: '要开始使用 SpheroSeg，您需要一个经过授权的账户。',
        step1Prefix: '导航到',
        step1Link: '请求访问页面',
        step2: '填写所需的详细信息。',
        step3: '提交表单。',
        step4: '等待管理员批准。',
      },
      creatingProject: {
        title: '创建您的第一个项目',
        paragraph1: '项目可以帮助您有效地组织图像和分析。',
        step1: '登录您的账户。',
        step2: '前往仪表板。',
        step3: "点击'新项目'。",
        step4: '为您的项目输入名称和描述。',
      },
    },
    uploadingImages: {
      title: '上传图像',
      paragraph1: '一旦您有了项目，您就可以上传您的显微镜图像进行分析。',
      methods: {
        title: '上传方法',
        paragraph1: '您可以使用以下方法上传图像：',
        step1: '导航到您的项目。',
        step2: "点击'上传图像'按钮。",
        step3: '拖放您的文件或点击选择它们。',
      },
      note: {
        prefix: '注意：',
        text: '确保您的图像格式为接受的格式（JPEG、PNG、TIFF、BMP）并且在大小限制内。',
      },
    },
    segmentationProcess: {
      title: '分割过程',
      paragraph1: 'SpheroSeg 提供自动和手动分割工具，以进行精确分析。',
      automatic: {
        title: '自动分割',
        paragraph1: '我们的人工智能模型可以高精度地自动分割类器官。',
        step1: '选择您要分析的图像。',
        step2: '选择所需的分割模型（如适用）。',
        step3: '启动分析过程。',
        step4: '处理完成后将显示结果。',
      },
      manual: {
        title: '手动编辑工具',
        paragraph1: '您可以使用我们的工具精细调整自动分割或手动创建新的分割。',
        step1: '在编辑器中打开图像。',
        step2: '选择适当的工具（例如，添加多边形，编辑顶点）。',
        step3: '在图像画布上进行调整。',
        step4: '定期保存您的更改。',
      },
    },
    apiReference: {
      title: 'API 参考',
      paragraph1: '对于高级用户，SpheroSeg 提供了 REST API 以便与其他系统集成。',
      endpoint1Desc: '获取您的项目列表。',
      endpoint2Desc: '获取特定项目中的图像。',
      endpoint3Desc: '为特定图像启动分割。',
      contactPrefix: '<EMAIL>',
    },
    backToHome: '返回首页',
    backToTop: '返回顶部',
  },
  dashboard: {
    manageProjects: '管理您的研究项目和分析',
    statsOverview: '统计概览',
    totalProjects: '总项目数',
    activeProjects: '活跃项目',
    totalImages: '总图片数',
    totalAnalyses: '总分析数',
    lastUpdated: '最近更新',
    noProjectsDescription: '您尚未创建任何项目。创建您的第一个项目以开始。',
    noImagesDescription: '上传一些图片以开始',
    searchProjectsPlaceholder: '搜索项目...',
    searchImagesPlaceholder: '按名称搜索图片...',
    sortBy: '排序方式',
    name: '名称',
    lastChange: '最近更改',
    status: '状态',
    completed: '已完成',
    processing: '处理中',
    pending: '待处理',
    failed: '失败',
    selectImagesButton: 'Select Images',
    viewMode: {
      grid: '网格视图',
      list: '列表视图',
    },
    sort: {
      name: '名称',
      updatedAt: '最后更新',
      segmentationStatus: '状态',
    },
    search: '搜索项目...',
    noProjects: '未找到项目',
    createFirst: '创建您的第一个项目以开始',
    createNew: '创建新项目',
  },
  projects: {
    createProject: '创建新项目',
    createProjectDesc: '添加新项目以组织您的类器官图像和分析。',
    projectNamePlaceholder: '例如：HeLa 细胞类器官',
    projectDescPlaceholder: '例如：用于药物抗性研究的肿瘤类器官分析',
    creatingProject: '创建中...',
    duplicateProject: '复制',
    shareProject: '分享',
    deleteProject: '删除',
    openProject: '打开项目',
    confirmDelete: '您确定要删除此项目吗？',
    projectCreated: '项目创建成功',
    projectDeleted: '项目删除成功',
    viewProject: '查看项目',
    projectImages: '项目图像',
    projectSelection: '项目选择',
    selectProject: '选择一个项目',
    projectNameRequired: '请输入项目名称',
    loginRequired: '您必须登录才能创建项目',
    createSuccess: '项目创建成功',
    createError: '创建项目失败',
    invalidData: '项目数据无效',
    title: '项目',
    description: 'Manage your research projects',
    createNew: '创建新项目',
    projectName: '项目名称',
    projectDescription: '项目描述',
    projectDescriptionPlaceholder: '输入项目描述',
    projectCreationFailed: '创建项目失败',
    projectDeletionFailed: '删除项目失败',
    confirmDeleteDescription: '此操作无法撤消。与此项目相关的所有数据将被永久删除。',
    editProject: '编辑项目',
    projectUpdated: '项目更新成功',
    projectUpdateFailed: '更新项目失败',
    noProjects: '未找到项目',
    createFirstProject: '创建您的第一个项目以开始',
    searchProjects: '搜索项目...',
    filterProjects: '筛选项目',
    sortProjects: '排序项目',
    createdAt: '创建于',
    updatedAt: '最后更新',
    imageCount: '图片',
    status: '状态',
    actions: '操作',
    loading: '加载项目中...',
    error: '加载项目出错',
    retry: '重试',
  },
  projectActions: {
    duplicateTooltip: '复制项目',
    deleteTooltip: '删除项目',
    deleteConfirmTitle: '您确定吗？',
    deleteConfirmDesc: "您确实要删除项目'{{projectName}}'吗？此操作无法撤消。",
    deleteSuccess: "项目'{{projectName}}'已成功删除。",
    deleteError: '删除项目失败。',
    duplicateSuccess: "项目'{{projectName}}'已成功复制。",
    duplicateError: '复制项目失败。',
    makePrivateTooltip: '设为私有',
    makePublicTooltip: '设为公开',
    shareTooltip: '分享项目',
    downloadTooltip: '下载项目',
    notFound: "找不到项目'{{projectName}}'。它可能已被删除。",
  },
  projectToolbar: {
    selectImages: '选择图像',
    cancelSelection: '取消选择',
    export: '导出',
    uploadImages: '上传图片',
  },
  images: {
    uploadImages: '上传图像',
    dragDrop: '拖放图像到这里',
    clickToSelect: '或点击选择文件',
    acceptedFormats: '接受的格式：JPEG、PNG、TIFF、BMP（最大 10MB）',
    uploadProgress: '上传进度',
    uploadingTo: '请先选择一个项目',
    currentProject: '当前项目',
    autoSegment: '上传后自动分割图像',
    uploadCompleted: '上传完成',
    uploadFailed: '上传失败',
    imagesUploaded: '图像上传成功',
    imagesFailed: '上传图像失败',
    viewAnalyses: '查看分析',
    noAnalysesYet: '尚无分析',
    runAnalysis: '运行分析',
    viewResults: '查看结果',
    dropImagesHere: '将图像拖放到这里...',
    selectProjectFirst: '请先选择一个项目',
    projectRequired: '您必须先选择一个项目才能上传图像',
    imageOnly: '（仅限图像文件）',
    dropFiles: '将文件拖放到这里...',
    filesToUpload: '要上传的文件 ({{count}})',
    uploadBtn: '上传 {{count}} 个图像',
    uploadError: '上传过程中发生错误。请重试。',
    noProjectsToUpload: '没有可用的项目。请先创建一个项目。',
    notFound: "找不到项目'{{projectName}}'。它可能已被删除。",
  },
  settings: {
    manageSettings: '管理您的账户偏好设置',
    appearance: '外观',
    themeSettings: '主题设置',
    systemDefault: '系统默认',
    languageSettings: '语言设置',
    selectLanguage: '选择语言',
    changeLanguage: '更改语言',
    useBrowserLanguage: '使用浏览器语言',
    accountSettings: '账户设置',
    profileSettings: '个人资料设置',
    profileUpdated: '个人资料更新成功',
    profileUpdateFailed: '个人资料更新失败',
    saveChanges: '保存更改',
    savingChanges: '保存中...',
    pageTitle: '设置',
    profile: '个人资料',
    account: '账户',
    profileTitle: '个人资料信息',
    profileDescription: '更新您的个人资料信息，该信息对其他用户可见',
    username: '用户名',
    usernamePlaceholder: '输入您的用户名',
    fullNamePlaceholder: '输入您的全名',
    title: '职称',
    titlePlaceholder: '例如：研究科学家，教授',
    organizationPlaceholder: '输入您的组织或机构',
    bio: '个人简介',
    bioPlaceholder: '写一个关于您自己的简短介绍',
    bioDescription: '简要描述您的研究兴趣和专长',
    location: '位置',
    locationPlaceholder: '例如：北京，中国',
    fetchError: '获取个人资料数据时出错',
    updateSuccess: '个人资料更新成功',
    updateError: '更新个人资料失败',
    noChanges: '没有要保存的更改',
    profileLoadError: '加载个人资料数据失败',
    languageUpdated: '语言更新成功',
    themeUpdated: '主题更新成功',
    appearanceDescription: '自定义应用程序的外观',
    languageDescription: '选择您的首选语言',
    personal: '个人信息',
    fullName: '全名',
    organization: '组织',
    department: '部门',
    publicProfile: '公开个人资料',
    makeProfileVisible: '使您的个人资料对其他研究人员可见',
    dangerZone: '危险区域',
    deleteAccountWarning: '一旦您删除您的账户，就无法撤消。您的所有数据将被永久删除。',
    currentPassword: '当前密码',
    newPassword: '新密码',
    confirmNewPassword: '确认新密码',
    securitySettings: '安全设置',
    preferenceSettings: '偏好设置',
    selectTheme: '选择主题',
    updateProfile: '更新个人资料',
    changePassword: '更改密码',
    deleteAccount: '删除账户',
    savedChanges: '更改保存成功',
    privacySettings: '隐私设置',
    exportData: '导出数据',
    importData: '导入数据',
    uploadAvatar: '上传头像',
    removeAvatar: '删除头像',
    twoFactorAuth: '双因素认证',
    weeklyDigest: '每周摘要',
    monthlyReport: '每月报告',
    displaySettings: '显示设置',
    accessibilitySettings: '无障碍设置',
    advancedSettings: '高级设置',
    language: '语言',
    theme: '主题',
    light: '浅色',
    dark: '深色',
    system: '系统',
    themeDescription: '选择您喜欢的主题',
  },
  auth: {
    signIn: '登录',
    signUp: '注册',
    signOut: '退出',
    forgotPassword: '忘记密码？',
    forgotPasswordTitle: '重置您的密码',
    checkYourEmail: '查看您的邮箱获取新密码',
    enterEmailForReset: '输入您的电子邮件地址，我们将向您发送新密码',
    passwordResetLinkSent: '如果此电子邮件地址存在账户，已发送新密码',
    passwordResetFailed: '发送新密码失败。请重试。',
    enterEmail: '请输入您的电子邮件地址',
    sendingResetLink: '正在发送新密码...',
    sendResetLink: '发送新密码',
    backToSignIn: '返回登录',
    resetPassword: '重置密码',
    dontHaveAccount: '没有账户？',
    alreadyHaveAccount: '已有账户？',
    signInWith: '使用以下方式登录',
    signUpWith: '使用以下方式注册',
    orContinueWith: '或继续使用',
    rememberMe: '记住我',
    emailRequired: '邮箱是必填项',
    passwordRequired: '密码是必填项',
    invalidEmail: '无效的邮箱地址',
    passwordTooShort: '密码至少需要6个字符',
    passwordsDontMatch: '密码不匹配',
    mustAgreeTerms: '您必须同意条款和条件',
    signUpSuccessEmail: '注册成功！请检查您的邮箱或等待管理员批准。',
    signUpFailed: '注册失败。请重试。',
    successfulSignIn: '登录成功',
    successfulSignUp: '注册成功',
    verifyEmail: '请检查您的邮箱以确认您的账户',
    successfulSignOut: '退出成功',
    checkingAuthentication: '正在检查身份验证...',
    loadingAccount: '正在加载您的账户...',
    processingRequest: '正在处理您的请求...',
    firstNamePlaceholder: '例如：小明',
    lastNamePlaceholder: '例如：王',
    emailPlaceholder: '<EMAIL>',
    passwordPlaceholder: '••••••••',
    alreadyLoggedInTitle: '您已经登录',
    alreadyLoggedInMessage: '您已经登录到您的账户。',
    goToDashboardLink: '前往仪表板',
    emailAddressLabel: '电子邮箱地址',
    passwordLabel: '密码',
    signingIn: '正在登录...',
    fillAllFields: '请填写所有字段',
    signInToAccount: '登录您的账户',
    accessPlatform: '访问类器官分割平台',
    requestAccess: '请求访问',
    termsAndPrivacy: '登录即表示您同意我们的服务条款和隐私政策',
    createAccount: '创建账户',
    signInWithGoogle: '使用 Google 登录',
    signInWithGithub: '使用 GitHub 登录',
    or: '或',
    signInTitle: '登录',
    signInDescription: '登录您的账户',
    noAccount: '没有账户？',
    currentPasswordLabel: '当前密码',
    newPasswordLabel: '新密码',
    confirmPasswordLabel: '确认密码',
    invalidCredentials: '无效的电子邮件或密码',
    accountCreated: '账户创建成功',
    resetLinkSent: '密码重置链接已发送到您的电子邮件',
    resetSuccess: '密码重置成功',
    signInSuccess: '登录成功',
    signOutSuccess: '退出登录成功',
    sessionExpired: '您的会话已过期。请重新登录。',
    unauthorized: '您无权访问此资源',
    verificationLinkSent: '验证链接已发送到您的电子邮件',
    verificationSuccess: '电子邮件验证成功',
    resendVerification: '重新发送验证电子邮件',
    forgotPasswordLink: '忘记密码？',
    passwordChanged: '密码已成功更改',
    currentPasswordIncorrect: '当前密码不正确',
    registerTitle: '创建账户',
    registerDescription: '注册新账户',
    registerSuccess: '注册成功！您现在可以登录。',
  },
  requestAccessForm: {
    title: '请求访问类器官分割平台',
    description: '填写以下表格以请求访问我们的平台。我们将审核您的请求并尽快回复您。',
    emailLabel: '您的电子邮件地址',
    nameLabel: '您的姓名',
    institutionLabel: '机构/公司',
    reasonLabel: '访问原因',
    submitButton: '提交请求',
    signInPrompt: '已有账户？',
    signInLink: '登录',
    thankYouTitle: '感谢您的关注',
    weWillContact: '我们将审核您的请求并尽快与您联系',
    agreeToTerms: '提交此请求即表示您同意我们的',
    and: '和',
  },
  profile: {
    title: '标题',
    about: '关于',
    activity: '活动',
    projects: '项目',
    recentProjects: '最近的项目',
    recentAnalyses: '最近的分析',
    accountDetails: '账户详情',
    accountType: '账户类型',
    joinDate: '注册日期',
    lastActive: '最后活动',
    projectsCreated: '已创建的项目',
    imagesUploaded: '已上传的图像',
    segmentationsCompleted: '已完成的分割',
    pageTitle: '用户资料',
    editProfile: '编辑资料',
    joined: '已加入',
    statistics: '统计数据',
    images: '图像',
    analyses: '分析',
    storageUsed: '已使用的存储空间',
    recentActivity: '最近活动',
    noRecentActivity: '没有最近活动',
    fetchError: '获取资料数据失败',
    aboutMe: '关于我',
    noBio: '未提供个人简介',
    avatarHelp: '点击相机图标上传头像',
    avatarImageOnly: '请选择图像文件',
    avatarTooLarge: '图像必须小于5MB',
    avatarUpdated: '头像已更新',
    avatarUploadError: '上传头像失败',
    avatarRemoved: '头像已移除',
    avatarRemoveError: '移除头像失败',
    description: '更新您的个人信息和头像',
    saveButton: '保存资料',
    username: '用户名',
    usernamePlaceholder: '输入您的用户名',
    fullName: '全名',
    fullNamePlaceholder: '输入您的全名',
    titlePlaceholder: '输入您的职称或职位',
    organization: '组织',
    organizationPlaceholder: '输入您的组织或公司',
    bio: '个人简介',
    bioPlaceholder: '告诉我们关于您自己的情况',
    bioDescription: '关于您自己的简短描述，将在您的个人资料中显示',
    location: '位置',
    locationPlaceholder: '输入您的位置',
    profileTitle: '用户资料',
  },
  hero: {
    platformTag: '先进的类器官分割平台',
    title: '用于生物医学研究的人工智能细胞分析',
    subtitle: '使用我们先进的类器官分割平台提升您的显微细胞图像分析。专为寻求精确度和效率的研究人员设计。',
    getStartedButton: '开始使用',
    learnMoreButton: '了解更多',
    imageAlt1: '类器官显微图像',
    imageAlt2: '带分析的类器官显微图像',
    welcomeTitle: '欢迎使用SpheroSeg',
    welcomeSubtitle: '用于细胞类器官分割和分析的先进平台',
    welcomeDescription:
      '我们的平台结合了先进的人工智能算法和直观的界面，为显微镜图像中的细胞类器官提供精确检测和分析。',
    featuresTitle: '强大功能',
    featuresSubtitle: '生物医学研究的高级工具',
    featureAiSegmentation: '高级分割',
    featureAiSegmentationDesc: '精确的类器官检测与边界分析，实现准确的细胞测量。',
    featureEditing: '人工智能分析',
    featureEditingDesc: '利用深度学习算法进行自动化的细胞检测和分类。',
    featureAnalytics: '轻松上传',
    featureAnalyticsDesc: '拖放您的显微图像，即可进行即时处理和分析。',
    featureExport: '统计见解',
    featureExportDesc: '全面的指标和可视化，以提取有意义的数据模式。',
    ctaTitle: '准备好改变您的细胞分析工作流程了吗？',
    ctaSubtitle: '加入已经在使用我们平台加速发现的顶尖研究人员行列。',
    ctaButton: '创建账户',
  },
  navbar: {
    home: '首页',
    features: '功能',
    documentation: '文档',
    terms: '条款',
    privacy: '隐私',
    login: '登录',
    requestAccess: '请求访问',
  },
  footer: {
    developerName: 'Bc. Michal Průšek',
    facultyName: 'FJFI ČVUT v Praze',
    copyrightNotice: '© 2025 SpheroSeg.',
    description: '先进的细胞类球体分割和分析平台',
    contactLabel: '<EMAIL>',
    developerLabel: 'Bc. Michal Průšek',
    facultyLabel: '布拉格FJFI ČVUT',
    resourcesTitle: '资源',
    documentationLink: '文档',
    featuresLink: '功能',
    tutorialsLink: '教程',
    researchLink: '研究',
    legalTitle: '法律信息',
    termsLink: '服务条款',
    privacyLink: '隐私政策',
    contactUsLink: '联系我们',
    informationTitle: '信息',
    contactTitle: '联系',
  },
  features: {
    tag: '功能',
    title: '探索我们平台的能力',
    subtitle: '生物医学研究的先进工具',
    cards: {
      segmentation: {
        title: '高级分割',
        description: '精确的类球体检测与边界分析，实现精确的细胞测量',
      },
      aiAnalysis: {
        title: 'AI驱动分析',
        description: '利用深度学习算法进行自动化细胞检测和分类',
      },
      uploads: {
        title: '简易上传',
        description: '拖放您的显微镜图像，立即进行处理和分析',
      },
      insights: {
        title: '统计洞察',
        description: '全面的指标和可视化，提取有意义的数据模式',
      },
      collaboration: {
        title: '团队协作',
        description: '与同事共享项目和结果，提高研究效率',
      },
      pipeline: {
        title: '自动化流程',
        description: '使用我们的批处理工具简化您的工作流程',
      },
    },
  },
  index: {
    about: {
      tag: '关于平台',
      title: '什么是SpheroSeg？',
      imageAlt: '类球体分割示例',
      paragraph1: 'SpheroSeg是一个专为显微镜图像中的细胞类球体分割和分析而设计的先进平台。',
      paragraph2: '我们的工具结合了最先进的人工智能算法和直观的界面，为研究人员提供精确的类球体边界检测和分析能力。',
      paragraph3:
        '该平台由布拉格FNSPE CTU的Michal Průšek在UTIA CAS的Adam Novozámský的指导下开发，并与UCT布拉格生物化学与微生物学系的研究人员合作完成。',
      contactPrefix: '<EMAIL>',
    },
    cta: {
      title: '准备好改变您的研究方式了吗？',
      subtitle: '立即开始使用SpheroSeg，探索细胞类球体分析的新可能性',
      boxTitle: '创建免费账户',
      boxText: '获取平台所有功能的访问权限，开始分析您的显微镜图像',
      button: '创建账户',
    },
  },
  tools: {
    zoomIn: '放大',
    zoomOut: '缩小',
    resetView: '重置视图',
    createPolygon: '创建新多边形',
    exitPolygonCreation: '退出多边形创建模式',
    splitPolygon: '将多边形分成两部分',
    exitSlicingMode: '退出切片模式',
    addPoints: '向多边形添加点',
    exitPointAddingMode: '退出添加点模式',
    undo: '撤销',
    redo: '重做',
    save: '保存',
    resegment: '重新分割',
    title: '工具',
  },
  shortcuts: {
    button: '快捷键',
    editMode: '切换到编辑模式',
    sliceMode: '切换到切片模式',
    addPointMode: '切换到添加点模式',
    holdShift: '按住Shift键自动添加点（在编辑模式下）',
    undo: '撤销',
    redo: '重做',
    deletePolygon: '删除多边形',
    cancel: '取消当前操作',
    zoomIn: '放大',
    zoomOut: '缩小',
    resetView: '重置视图',
    title: '键盘快捷键',
    viewMode: '查看模式',
    editVerticesMode: '编辑顶点模式',
    addPointsMode: '添加点模式',
    createPolygonMode: '创建多边形模式',
    save: '保存',
    description: '这些快捷键在分割编辑器中工作，以实现更快速和更方便的操作。',
  },
  imageProcessor: {
    segmentationStarted: '分割过程已开始...',
    startSegmentationTooltip: '开始分割',
    processingTooltip: '处理中...',
    savingTooltip: '保存中...',
    completedTooltip: '分割完成',
    retryTooltip: '重试分割',
  },
  uploader: {
    dragDrop: '将图片拖放到这里或点击选择文件',
    dropFiles: '将文件拖放到这里...',
    segmentAfterUploadLabel: '上传后立即对图片进行分割',
    filesToUpload: '要上传的文件',
    uploadBtn: '上传',
    uploadError: '上传过程中出现错误。请重试。',
    clickToUpload: '点击浏览文件',
    selectProjectLabel: '选择项目',
    selectProjectPlaceholder: '选择一个项目...',
    noProjectsFound: '未找到项目。请先创建一个。',
    imageOnly: '(仅限图片文件)',
    uploadingFile: '上传 {{current}}/{{total}}: {{filename}}',
  },
  export: {
    exportCompleted: '导出完成',
    exportFailed: '导出失败',
    title: '导出分割数据',
    spheroidMetrics: '类球体指标',
    visualization: '可视化',
    cocoFormat: 'COCO格式',
    close: '关闭',
    metricsExported: '指标导出成功',
    backToProject: '返回项目',
    exportImages: '导出 {count} 张图片',
    exporting: '导出中...',
    selectImagesForExport: '选择要导出的图片',
    selectAll: '全选',
    deselectAll: '取消全选',
    noImagesAvailable: '没有可用的图片',
    options: {
      includeMetadata: '包含元数据',
      includeSegmentation: '包含分割',
      includeObjectMetrics: '包含对象指标',
      includeImages: '包含图片',
      selectExportFormat: '选择导出格式',
      selectMetricsFormat: '选择指标格式',
      exportMetricsOnly: '仅导出指标',
      metricsFormatDescription: {
        EXCEL: '带有多个工作表的Excel格式',
        CSV: '简单的CSV格式，便于导入',
      },
    },
    formats: {
      COCO: 'COCO JSON（上下文中的常见对象）',
      YOLO: 'YOLO（你只看一次）',
      MASK: '二进制掩码',
      POLYGONS: 'GeoJSON多边形',
    },
    formatDescriptions: {
      COCO: 'COCO JSON是对象检测和分割的标准格式。',
      YOLO: 'YOLO格式使用归一化坐标表示边界框。',
      MASK: '二进制掩码是黑白图像，其中白色表示对象。',
      POLYGONS: '多边形以JSON格式存储，包含(x,y)点。',
    },
    metricsFormats: {
      EXCEL: 'Excel电子表格',
      CSV: 'CSV文件',
    },
    metricsRequireSegmentation: '指标需要可用的分割数据。',
  },
  metrics: {
    area: '面积',
    perimeter: '周长',
    circularity: '圆度',
    sphericity: '球形度',
    solidity: '实度',
    compactness: '紧凑度',
    convexity: '凸度',
    visualization: '指标可视化',
    visualizationHelp: '此图像中所有类球体的指标可视化表示',
    barChart: '柱状图',
    pieChart: '饼图',
    comparisonChart: '比较图',
    keyMetricsComparison: '关键指标比较',
    areaDistribution: '面积分布',
    shapeMetricsComparison: '形状指标比较',
    noPolygonsFound: '未找到可分析的多边形',
  },
  imageStatus: {
    completed: '已处理',
    processing: '处理中',
    pending: '等待中',
    failed: '失败',
    noImage: '无图片',
    untitledImage: '无标题图片',
  },
  segmentation: {
    resolution: '分辨率',
    selectPolygonForSlice: '选择一个多边形进行切片',
    selectPolygonForAddPoints: '选择一个多边形添加点',
    selectPolygonForEdit: '选择一个多边形进行编辑',
    status: {
      processing: '处理中',
      queued: '排队中',
      completed: '已完成',
      failed: '失败',
      pending: '待处理',
    },
    queue: {
      title: '分割队列',
      summary: '共 {{total}} 个任务（{{running}} 个处理中，{{queued}} 个排队中）',
      noRunningTasks: '没有正在运行的任务',
      noQueuedTasks: '没有排队中的任务',
      task: '任务',
      statusRunning: '分割：{{count}} 个运行中{{queued}}',
      statusQueued: '，{{count}} 个排队中',
      statusOnlyQueued: '分割：{{count}} 个排队中',
      statusOnlyQueued_one: '分割：1 个排队中',
      statusOnlyQueued_other: '分割：{{count}} 个排队中',
      processing: '处理中',
      queued: '排队中',
      statusProcessing: '分割：{{count}} 个处理中',
      statusReady: '分割：就绪',
      tasksTotal: '共 {{total}} 个任务（{{running}} 个处理中，{{queued}} 个排队中）'
    },
      completed: '分割成功完成',
      failed: '分割失败：{{error}}',
      queued: '分割已加入队列',
      started: '分割已开始',
    },
    autoSave: {
      enabled: '自动保存：已启用',
      disabled: '自动保存：关闭',
      idle: '自动保存：空闲',
      pending: '等待中...',
      saving: '保存中...',
      success: '已保存',
      error: '错误',
    },
    loading: '加载分割中...',
    polygon: '多边形',
    unsavedChanges: '未保存的更改',
    modes: {
      editMode: '编辑模式',
      slicingMode: '切片模式',
      pointAddingMode: '添加点模式',
      view: '查看',
      editVertices: '编辑顶点',
      addPoints: '添加点',
      slice: '切片',
      createPolygon: '创建多边形',
      deletePolygon: '删除多边形',
    },
    totalPolygons: '多边形总数',
    totalVertices: '顶点总数',
    completedSegmentation: '已完成',
    mode: '模式',
    segmentationLoading: '加载分割中...',
    segmentationPolygon: '多边形',
    saveSuccess: '分割成功保存',
    resegmentSuccess: '重新分割成功启动',
    resegmentComplete: '重新分割成功完成',
    resegmentError: '重新分割图像失败',
    resegmentButton: '重新分割',
    resegmentButtonTooltip: '对此图像再次运行分割',
    polygonDeleted: '多边形成功删除',
    vertices: '顶点',
    zoom: '缩放',
    position: '位置',
    selected: '已选择',
    none: '无',
    polygons: '多边形',
    noData: '没有可用的分割数据',
    noPolygons: '未找到多边形',
    regions: '分割',
    processingSegmentation: '处理中',
    pendingSegmentation: '等待中',
    helpTips: {
      title: '提示：',
      edit: {
        createPoint: '点击创建新点',
        shiftPoints: '按住Shift键自动创建一系列点',
        closePolygon: '点击第一个点关闭多边形',
      },
      slice: {
        start: '点击开始切片',
        finish: '再次点击完成切片',
        cancel: '按Esc取消切片',
      },
      addPoint: {
        hover: '悬停在多边形线上',
        click: '点击向选定的多边形添加点',
        exit: '按Esc退出添加点模式',
      },
    },
  termsPage: {
    title: 'Terms of Service',
    acceptance: {
      title: '1. Acceptance of Terms',
      paragraph1:
        'By accessing or using SpheroSeg, you agree to be bound by these Terms of Service and all applicable laws and regulations. If you do not agree with any of these terms, you are prohibited from using this service.',
    },
    useLicense: {
      title: '2. Use License',
      paragraph1:
        'Permission is granted to temporarily use SpheroSeg for personal, non-commercial, or academic research purposes only. This is the grant of a license, not a transfer of title.',
    },
    dataUsage: {
      title: '3. Data Usage',
      paragraph1:
        'Any data uploaded to SpheroSeg remains your property. We do not claim ownership of your content but require certain permissions to provide the service.',
    },
    limitations: {
      title: '4. Limitations',
      paragraph1:
        'In no event shall SpheroSeg be liable for any damages arising out of the use or inability to use the platform, even if we have been notified of the possibility of such damage.',
    },
    revisions: {
      title: '5. Revisions and Errata',
      paragraph1:
        'The materials appearing on SpheroSeg could include technical, typographical, or photographic errors. We do not warrant that any of the materials are accurate, complete, or current.',
    },
    governingLaw: {
      title: '6. Governing Law',
      paragraph1:
        'These terms and conditions are governed by and construed in accordance with the laws of the country in which the service is hosted, and you irrevocably submit to the exclusive jurisdiction of the courts in that location.',
    },
  },
  statsOverview: {
    totalProjects: '总项目数',
    totalProjectsDesc: '本月新增 {count} 个',
    totalImages: '总图片数',
    totalImagesDesc: '增长 {count, number}%',
    completedSegmentations: '已完成的分割',
    completedSegmentationsDesc: '比上一时期多 {count} 个',
    segmentationsToday: '今日分割',
    segmentationsTodayDesc: '比昨天多 {count} 个',
    fetchError: '无法加载统计数据。',
    loadError: '无法加载统计数据。',
    title: '仪表板概览',
    storageUsed: '已使用存储空间',
    recentActivity: '最近活动',
    moreStats: '查看详细统计信息',
    completion: '完成率',
    vsLastMonth: '与上月相比',
    thisMonth: '本月',
    lastMonth: '上月',
    projectsCreated: '创建的项目',
    imagesUploaded: '上传的图片',
    storageLimit: '存储限制',
    activityTitle: '最近活动',
    noActivity: '没有最近活动',
    hide: '隐藏',
    activityTypes: {
      project_created: '创建了项目',
      image_uploaded: '上传了图片',
      segmentation_completed: '完成了分割',
    },
  },
  privacyPage: {
    title: 'Privacy Policy',
    introduction: {
      title: '1. Introduction',
      paragraph1:
        'This Privacy Policy explains how SpheroSeg ("we", "us", "our") collects, uses, and shares your information when you use our platform for spheroid segmentation and analysis.',
    },
    informationWeCollect: {
      title: '2. Information We Collect',
      paragraph1:
        'We collect information you provide directly to us when you create an account, upload images, create projects, and otherwise interact with our services.',
    },
    personalInformation: {
      title: '2.1 Personal Information',
      paragraph1:
        'This includes your name, email address, institution/organization, and other information you provide when setting up your account or requesting access to our services.',
    },
    researchData: {
      title: '2.2 Research Data',
      paragraph1:
        'This includes images you upload, project details, analysis results, and other research related data you create or upload to our platform.',
    },
    usageInformation: {
      title: '2.3 Usage Information',
      paragraph1:
        'We collect information about how you use our platform, including log data, device information, and usage patterns.',
    },
    howWeUse: {
      title: '3. How We Use Your Information',
      paragraph1:
        'We use the information we collect to provide, maintain, and improve our services, to communicate with you, and to comply with our legal obligations.',
    },
    dataSecurity: {
      title: '4. Data Security',
      paragraph1:
        'We implement appropriate security measures to protect your personal information and research data from unauthorized access, alteration, disclosure, or destruction.',
    },
    dataSharing: {
      title: '5. Data Sharing',
      paragraph1:
        'We do not sell your personal information or research data. We may share your information in limited circumstances, such as with your consent, to comply with legal obligations, or with service providers who help us operate our platform.',
    },
    yourChoices: {
      title: '6. Your Choices',
      paragraph1:
        'You can access, update, or delete your account information and research data through your account settings. You can also contact us to request access to, correction of, or deletion of any personal information we have about you.',
    },
    changes: {
      title: '7. Changes to This Policy',
      paragraph1:
        'We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the "Last Updated" date.',
    },
    contactUs: {
      title: '8. Contact Us',
      paragraph1: 'If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.',
    },
    lastUpdated: 'Last Updated: July 1, 2023',
  },
  editor: {
    backButtonTooltip: '返回项目概览',
    exportButtonTooltip: '导出当前分割数据',
    saveTooltip: '保存您的更改',
    image: '图像',
    previousImage: '上一张图像',
    nextImage: '下一张图像',
    resegmentButton: '重新分割',
    resegmentButtonTooltip: '对此图像再次运行分割',
    exportMaskButton: '导出掩码',
    exportMaskButtonTooltip: '导出此图像的分割掩码',
    backButton: '返回',
    exportButton: '导出',
    saveButton: '保存',
    loadingProject: '加载项目中...',
    loadingImage: '加载图像中...',
    sliceErrorInvalidPolygon: '无法切片：选择的多边形无效。',
    sliceWarningInvalidResult: '切片导致多边形太小而无效。',
    sliceWarningInvalidIntersections: '切片无效：切割线必须恰好在两点处与多边形相交。',
    sliceSuccess: '多边形切片成功。',
    noPolygonToSlice: '没有可切片的多边形。',
    savingTooltip: '保存中...',
  },
  segmentationPage: {
    noImageSelected: '没有选择图像进行重新分割。',
    resegmentationStarted: '正在使用ResUNet神经网络开始重新分割...',
    resegmentationQueued: '重新分割已加入队列。',
    resegmentationCompleted: '重新分割成功完成。',
    resegmentationFailed: '重新分割失败。',
    resegmentationTimeout: '重新分割超时。请检查队列状态。',
    resegmentationError: '启动重新分割失败。',
    resegmentTooltip: '重新分割',
    options: '分割选项',
    optionsTitle: '分割选项',
    modelType: '模型类型',
    threshold: '检测阈值',
    fillHoles: '填充孔洞',
    minSize: '最小对象大小',
    priority: '处理优先级',
    advanced: '高级',
    resegmentOptions: '重新分割选项',
    resegmentDefault: '默认（ResUNet）',
  },
  errors: {
    fetchSegmentationFailed: '加载分割数据失败',
    fetchImageFailed: '加载图像数据失败',
    saveSegmentationFailed: '保存分割失败',
    networkError: '发生网络错误',
    serverError: '发生服务器错误',
    unknownError: '发生未知错误',
    imageNotFound: '未找到图像',
    imageNotFoundDesc: '您尝试访问的图像不存在或已被删除。',
    returnToProject: '返回项目',
    goToDashboard: '前往仪表板',
    somethingWentWrong: '出现问题',
    componentError: '此组件发生错误',
    goBack: '返回',
    tryAgain: '重试',
    notFound: '未找到页面',
    unauthorized: '未授权的访问',
    forbidden: '访问被禁止',
    timeoutError: '请求超时',
    validationError: '验证错误',
    pageNotFound: '未找到页面',
    pageNotFoundMessage: '您请求的页面无法找到',
    goHome: '前往首页',
  },
  undo: {
    action: '撤销：操作已撤销',
    createPolygon: '撤销：多边形创建已撤销',
    deletePolygon: '撤销：多边形删除已撤销',
    editPolygon: '撤销：多边形编辑已撤销',
    slicePolygon: '撤销：多边形切片已撤销',
    moveVertex: '撤销：顶点移动已撤销',
    addVertex: '撤销：顶点添加已撤销',
    deleteVertex: '撤销：顶点删除已撤销',
    addPoints: '撤销：点添加已撤销',
    resegment: '撤销：重新分割已撤销',
    noActions: '没有可撤销的操作',
  },
  redo: {
    action: '重做：操作已恢复',
    createPolygon: '重做：多边形创建已恢复',
    deletePolygon: '重做：多边形删除已恢复',
    editPolygon: '重做：多边形编辑已恢复',
    slicePolygon: '重做：多边形切片已恢复',
    moveVertex: '重做：顶点移动已恢复',
    addVertex: '重做：顶点添加已恢复',
    deleteVertex: '重做：顶点删除已恢复',
    addPoints: '重做：点添加已恢复',
    resegment: '重做：重新分割已恢复',
    noActions: '没有可重做的操作',
  },
  history: {
    cleared: '历史记录已清除',
    saved: '历史记录已保存',
    restored: '历史记录已恢复',
  },
  project: {
    loading: '正在加载项目...',
    notFound: '未找到项目',
    error: '加载项目时出错',
    empty: '此项目为空',
    noImages: '此项目中未找到图像',
    addImages: '添加图像以开始',
  },
  navigation: {
    home: '首页',
    projects: '项目',
    settings: '设置',
    profile: '个人资料',
    dashboard: '仪表板',
    back: '返回',
  },
  accessibility: {
    skipToContent: '跳转到主要内容',
  },
  projectsPage: {
    title: '项目',
    description: '管理您的研究项目',
    createNew: '创建新项目',
    createProject: '创建项目',
    createProjectDesc: '开始一个新的研究项目',
    projectName: '项目名称',
    projectDescription: '项目描述',
    projectNamePlaceholder: '输入项目名称',
    projectDescriptionPlaceholder: '输入项目描述',
    projectCreated: '项目创建成功',
    projectCreationFailed: '项目创建失败',
    projectDeleted: '项目删除成功',
    projectDeletionFailed: '项目删除失败',
    confirmDelete: '您确定要删除此项目吗？',
    confirmDeleteDescription: '此操作无法撤销。与此项目相关的所有数据将被永久删除。',
    deleteProject: '删除项目',
    editProject: '编辑项目',
    viewProject: '查看项目',
    projectUpdated: '项目更新成功',
    projectUpdateFailed: '项目更新失败',
    noProjects: '未找到项目',
    createFirstProject: '创建您的第一个项目以开始',
    searchProjects: '搜索项目...',
    filterProjects: '筛选项目',
    sortProjects: '排序项目',
    projectNameRequired: '项目名称为必填项',
    loginRequired: '您必须登录才能创建项目',
    createdAt: '创建于',
    updatedAt: '最后更新',
    imageCount: '图像',
    status: '状态',
    actions: '操作',
    loading: '正在加载项目...',
    error: '加载项目时出错',
    retry: '重试',
    duplicating: '正在复制项目...',
    duplicate: '复制',
    duplicateSuccess: '项目复制成功',
    duplicateFailed: '项目复制失败',
    duplicateTitle: '复制项目',
    duplicateProject: '复制项目',
    duplicateProjectDescription: '创建此项目的副本，包括所有图像。您可以自定义以下选项。',
    duplicateCancelled: '项目复制已取消',
    duplicatingProject: '正在复制项目',
    duplicatingProjectDescription: '您的项目正在被复制。这可能需要一些时间。',
    duplicateProgress: '复制进度',
    duplicationComplete: '项目复制完成',
    duplicationTaskFetchError: '获取任务数据时出错',
    duplicationCancelError: '取消复制时出错',
    duplicateProgressDescription: '您的项目正在被复制。对于大型项目，此过程可能需要一些时间。',
    duplicationPending: '等待中',
    duplicationProcessing: '处理中',
    duplicationCompleted: '已完成',
    duplicationFailed: '失败',
    duplicationCancelled: '已取消',
    duplicationCancellationFailed: '取消复制失败',
    duplicationSuccessMessage: '项目复制成功！您现在可以访问新项目。',
    copySegmentations: '复制分割结果',
    resetImageStatus: '重置图像处理状态',
    newProjectTitle: '新项目标题',
    itemsProcessed: '已处理项目',
    items: '项目',
    unknownProject: '未知项目',
    activeTasks: '活跃',
    allTasks: '全部',
    noActiveDuplications: '没有活跃的复制',
    noDuplications: '未找到复制任务',
    deleteProjectDescription: '此操作将永久删除项目和所有相关数据。',
    deleteWarning: '此操作无法撤销。与此项目相关的所有数据将被永久删除。',
    untitledProject: '未命名项目',
    typeToConfirm: '输入"delete"以确认',
    deleteConfirm: '您确定要删除此项目吗？',
    exportProject: '导出项目',
    archived: '已归档',
    completed: '已完成',
    draft: '草稿',
    active: '活跃',
    createDate: '创建日期',
    lastModified: '最后修改',
    projectDescPlaceholder: '输入项目描述',
    creatingProject: '正在创建项目...',
  },
};
