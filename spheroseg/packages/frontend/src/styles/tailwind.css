@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 240 5.9% 10%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.75rem;
  }

  .dark {
    --background: 222.2 84% 5.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 6.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 5.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 19.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 19.5%;
    --muted-foreground: 215 20.2% 75%; /* Zvýšený kontrast pro lepší čitelnost */

    --accent: 217.2 32.6% 19.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 35.6%; /* Trochu světlejší pro lepší viditelnost */
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 19.5%;
    --input: 217.2 32.6% 19.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.3s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-pulse-once {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) 1;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  /* Glass effect */
  .glass {
    @apply bg-white/70 dark:bg-black/50 backdrop-blur-lg;
  }

  /* Card hover animation */
  .card-hover {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
  }

  /* Button press animation */
  .btn-press {
    @apply active:scale-95 transition-transform;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-400 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500 dark:bg-gray-500;
}

/* Modern gradients */
.bg-gradient-purple-blue {
  @apply bg-gradient-to-r from-purple-500 to-blue-500;
}

.bg-gradient-orange-pink {
  @apply bg-gradient-to-r from-orange-400 to-pink-500;
}

.bg-gradient-green-teal {
  @apply bg-gradient-to-r from-green-400 to-teal-500;
}

/* Custom shadows */
.shadow-soft {
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.05),
    0 8px 10px -6px rgba(0, 0, 0, 0.01);
}

.shadow-inner-light {
  box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);
}
