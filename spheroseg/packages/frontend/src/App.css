/* App-specific styles */

html,
body,
#root {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
}

/* Custom toast styles */
:root {
  --toast-padding: 1rem 1.25rem;
  --toast-border-radius: 0.5rem;
}

[data-sonner-toast] {
  padding: var(--toast-padding) !important;
  border-radius: var(--toast-border-radius) !important;
  min-width: 320px !important;
  max-width: 420px !important;
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 8px 10px -6px rgba(0, 0, 0, 0.05) !important;
}

[data-sonner-toast] [data-description] {
  margin-top: 0.5rem !important;
  font-size: 0.9rem !important;
  line-height: 1.5 !important;
}

[data-sonner-toast] [data-title] {
  font-weight: 600 !important;
  font-size: 1rem !important;
  line-height: 1.5 !important;
}

[data-sonner-toast] [data-close-button] {
  opacity: 0.7 !important;
  transition: opacity 0.2s ease !important;
}

[data-sonner-toast] [data-close-button]:hover {
  opacity: 1 !important;
}

/* Custom toast class */
.custom-toast {
  font-family: 'Inter', sans-serif !important;
  font-size: 0.95rem !important;
  line-height: 1.5 !important;
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 8px 10px -6px rgba(0, 0, 0, 0.05) !important;
  border-width: 1px !important;
}

/* Toast types */
.custom-toast[data-type='success'] {
  border-left: 4px solid #10b981 !important;
}

.custom-toast[data-type='error'],
.error-toast {
  border-left: 4px solid #ef4444 !important;
  padding: 1.25rem 1.5rem !important;
  background-color: rgba(254, 242, 242, 0.95) !important;
  color: #991b1b !important;
}

.custom-toast[data-type='error'] [data-description],
.error-toast [data-description] {
  color: #b91c1c !important;
  margin-top: 0.75rem !important;
  font-size: 0.875rem !important;
}

.custom-toast[data-type='warning'] {
  border-left: 4px solid #f59e0b !important;
}

.custom-toast[data-type='info'] {
  border-left: 4px solid #3b82f6 !important;
}

/* Typography improvements */
body {
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Open Sans',
    'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  letter-spacing: -0.011em;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 600;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

h1 {
  font-size: 2.5rem;
  font-weight: 700;
}

h2 {
  font-size: 2rem;
  font-weight: 700;
}

h3 {
  font-size: 1.5rem;
}

h4 {
  font-size: 1.25rem;
}

p {
  line-height: 1.6;
}

.text-balance {
  text-wrap: balance;
}

/* Custom shadows */
.shadow-soft {
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.05),
    0 8px 10px -6px rgba(0, 0, 0, 0.01);
}

.shadow-inner-light {
  box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);
}

/* Skip link for accessibility */
.skip-link {
  position: absolute;
  top: -40px;
  left: 0;
  background: #000;
  color: white;
  padding: 8px;
  z-index: 100;
  transition: top 0.3s;
}

.skip-link:focus {
  top: 0;
}

/* Focus styles for accessibility */
:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--background));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground));
}

/* Animation for page transitions */
.page-transition-enter {
  opacity: 0;
}

.page-transition-enter-active {
  opacity: 1;
  transition: opacity 300ms;
}

.page-transition-exit {
  opacity: 1;
}

.page-transition-exit-active {
  opacity: 0;
  transition: opacity 300ms;
}

/* Staggered fade-in animation */
.staggered-fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition:
    opacity 0.5s ease-out,
    transform 0.5s ease-out;
}

.staggered-fade-in.active {
  opacity: 1;
  transform: translateY(0);
}

/* Modern gradients */
.bg-gradient-purple-blue {
  background: linear-gradient(to right, #a855f7, #3b82f6);
}

.bg-gradient-orange-pink {
  background: linear-gradient(to right, #fb923c, #ec4899);
}

.bg-gradient-green-teal {
  background: linear-gradient(to right, #4ade80, #14b8a6);
}

/* Float animation */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}
