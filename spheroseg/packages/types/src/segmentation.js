"use strict";
/**
 * Segmentation specific types
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SegmentationStatus = void 0;
var SegmentationStatus;
(function (SegmentationStatus) {
    SegmentationStatus["PENDING"] = "pending";
    SegmentationStatus["PROCESSING"] = "processing";
    SegmentationStatus["COMPLETED"] = "completed";
    SegmentationStatus["FAILED"] = "failed";
    SegmentationStatus["SAVING"] = "saving";
})(SegmentationStatus || (exports.SegmentationStatus = SegmentationStatus = {}));
//# sourceMappingURL=segmentation.js.map