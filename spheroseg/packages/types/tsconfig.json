{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ESNext"], "declaration": true, "outDir": "dist", "rootDir": "src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "composite": true, "declarationMap": true, "sourceMap": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}