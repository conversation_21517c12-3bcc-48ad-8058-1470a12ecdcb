<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Copy/Paste Detector Report</title><link href="styles/tailwind.css" rel="stylesheet"><link href="styles/prism.css" rel="stylesheet"></head><body class="bg-gray-100"><header class="bg-white shadow py-4"><div class="container mx-auto px-4"><h1 class="text-3xl font-semibold text-gray-800">jscpd - copy/paste report</h1></div></header><main class="container mx-auto my-8 p-4 bg-white shadow rounded"><section class="mb-8" id="dashboard"><h2 class="text-2xl font-semibold text-gray-700 mb-4">Dashboard</h2><div class="grid grid-cols-4 gap-4"><div class="bg-blue-200 p-4 rounded text-center"><h3 class="text-lg font-semibold text-blue-800 mb-2">Total Files</h3><span class="text-4xl font-bold text-blue-800">700</span></div><div class="bg-green-200 p-4 rounded text-center"><h3 class="text-lg font-semibold text-green-800 mb-2">Total Lines of Code</h3><span class="text-4xl font-bold text-green-800">84623</span></div><div class="bg-yellow-200 p-4 rounded text-center"><h3 class="text-lg font-semibold text-yellow-800 mb-2">Number of Clones</h3><span class="text-4xl font-bold text-yellow-800">164</span></div><div class="bg-red-200 p-4 rounded text-center"><h3 class="text-lg font-semibold text-red-800 mb-2">Duplicated Lines</h3><span class="text-4xl font-bold text-red-800">5114 (6.04%)</span></div></div></section><section class="mb-8" id="formats"><h2 class="text-2xl font-semibold text-gray-700 mb-4">Formats with Duplications</h2><table class="w-full table-auto"><thead><tr class="bg-gray-200 text-gray-600 uppercase text-sm leading-normal"><th class="py-3 px-6 text-left">Format</th><th class="py-3 px-6 text-left">Files</th><th class="py-3 px-6 text-left">Lines</th><th class="py-3 px-6 text-left">Clones</th><th class="py-3 px-6 text-left">Duplicated Lines</th><th class="py-3 px-6 text-left">Duplicated Tokens</th></tr></thead><tbody><tr class="bg-white border-b border-gray-200 text-gray-800 text-sm"><td class="py-3 px-6"><a class="text-blue-600 hover:underline" href="#typescript-clones">typescript</a></td><td class="py-3 px-6">227</td><td class="py-3 px-6">38849</td><td class="py-3 px-6">82</td><td class="py-3 px-6">2183</td><td class="py-3 px-6">19456</td></tr><tr class="bg-white border-b border-gray-200 text-gray-800 text-sm"><td class="py-3 px-6"><a class="text-blue-600 hover:underline" href="#tsx-clones">tsx</a></td><td class="py-3 px-6">259</td><td class="py-3 px-6">31922</td><td class="py-3 px-6">44</td><td class="py-3 px-6">628</td><td class="py-3 px-6">5120</td></tr><tr class="bg-white border-b border-gray-200 text-gray-800 text-sm"><td class="py-3 px-6"><a class="text-blue-600 hover:underline" href="#javascript-clones">javascript</a></td><td class="py-3 px-6">213</td><td class="py-3 px-6">13845</td><td class="py-3 px-6">38</td><td class="py-3 px-6">2303</td><td class="py-3 px-6">20712</td></tr><tr class="bg-white border-b border-gray-200 text-gray-800 text-sm"><td class="py-3 px-6"><a class="text-blue-600 hover:underline" href="#css-clones">css</a></td><td class="py-3 px-6">1</td><td class="py-3 px-6">7</td><td class="py-3 px-6">0</td><td class="py-3 px-6">0</td><td class="py-3 px-6">0</td></tr></tbody></table></section><section class="mb-8" id="txt-clones"><a name="typescript-clones"></a><h2 class="text-2xl font-semibold text-gray-700 mb-4">typescript</h2><div class="divide-y divide-gray-200 border-b-2"><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/hooks/useSegmentationKeyboard.ts (Line 117:6 - Line 130:12), packages/frontend/src/pages/segmentation/hooks/segmentation/useSegmentationV2.ts (Line 460:5 - Line 473:18)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn12" onclick="toggleCodeBlock('cloneGroup12', 'expandBtn12', 'collapseBtn12')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn12" onclick="toggleCodeBlock('cloneGroup12', 'expandBtn12', 'collapseBtn12')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup12"><code class="language-typescript text-sm text-gray-800">);
            }
        };

        window.addEventListener('keydown', handleKeyDown);
        window.addEventListener('keyup', handleKeyUp);

        return () =&gt; {
            window.removeEventListener('keydown', handleKeyDown);
            window.removeEventListener('keyup', handleKeyUp);
        };
    }, [
        editMode,
        setEditMode</code></pre></div><div class="py-4"><p class="text-gray-600">packages/shared/src/utils/geometry/geometryUtils.ts (Line 45:3 - Line 63:4), packages/frontend/src/pages/segmentation/utils/geometry.ts (Line 7:5 - Line 24:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn14" onclick="toggleCodeBlock('cloneGroup14', 'expandBtn14', 'collapseBtn14')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn14" onclick="toggleCodeBlock('cloneGroup14', 'expandBtn14', 'collapseBtn14')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup14"><code class="language-typescript text-sm text-gray-800">let inside = false;
  for (let i = 0, j = polygon.length - 1; i &lt; polygon.length; j = i++) {
    const xi = polygon[i].x;
    const yi = polygon[i].y;
    const xj = polygon[j].x;
    const yj = polygon[j].y;

    const intersect = ((yi &gt; point.y) !== (yj &gt; point.y)) &amp;&amp;
      (point.x &lt; (xj - xi) * (point.y - yi) / (yj - yi) + xi);

    if (intersect) inside = !inside;
  }

  return inside;
};

/**
 * Calculate the intersection point of two line segments
 */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/shared/src/utils/geometry/geometryUtils.ts (Line 205:2 - Line 214:2), packages/frontend/src/pages/segmentation/hooks/polygonInteraction/geometry/utils/intersectionUtils.ts (Line 81:2 - Line 88:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn15" onclick="toggleCodeBlock('cloneGroup15', 'expandBtn15', 'collapseBtn15')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn15" onclick="toggleCodeBlock('cloneGroup15', 'expandBtn15', 'collapseBtn15')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup15"><code class="language-typescript text-sm text-gray-800">(points: Point[]): number =&gt; {
  let area = 0;

  for (let i = 0; i &lt; points.length; i++) {
    const j = (i + 1) % points.length;
    area += points[i].x * points[j].y;
    area -= points[j].x * points[i].y;
  }

  return Math.abs(area /</code></pre></div><div class="py-4"><p class="text-gray-600">packages/shared/src/utils/geometry/geometryUtils.ts (Line 231:10 - Line 254:2), packages/frontend/src/pages/segmentation/utils/geometry.ts (Line 44:2 - Line 67:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn16" onclick="toggleCodeBlock('cloneGroup16', 'expandBtn16', 'collapseBtn16')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn16" onclick="toggleCodeBlock('cloneGroup16', 'expandBtn16', 'collapseBtn16')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup16"><code class="language-typescript text-sm text-gray-800">;
};

/**
 * Determines if a polygon is oriented clockwise
 */
export const isClockwise = (points: Point[]): boolean =&gt; {
  let sum = 0;
  for (let i = 0; i &lt; points.length; i++) {
    const j = (i + 1) % points.length;
    sum += (points[j].x - points[i].x) * (points[j].y + points[i].y);
  }
  return sum &gt; 0;
};

/**
 * Ensures a polygon's points are in clockwise order
 */
export const ensureClockwise = (points: Point[]): Point[] =&gt; {
  if (!isClockwise(points)) {
    return [...points].reverse();
  }
  return points;
};</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/shared/utils/polygonSlicingUtils.ts (Line 17:1 - Line 30:4), packages/shared/src/utils/geometry/geometryUtils.ts (Line 191:1 - Line 204:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn17" onclick="toggleCodeBlock('cloneGroup17', 'expandBtn17', 'collapseBtn17')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn17" onclick="toggleCodeBlock('cloneGroup17', 'expandBtn17', 'collapseBtn17')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup17"><code class="language-typescript text-sm text-gray-800">export const getPointSideOfLine = (
  point: Point,
  lineStart: Point,
  lineEnd: Point
): number =&gt; {
  return (
    (lineEnd.x - lineStart.x) * (point.y - lineStart.y) -
    (lineEnd.y - lineStart.y) * (point.x - lineStart.x)
  );
};

/**
 * Calculate the intersection point of two lines
 */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/shared/utils/polygonSlicingUtils.ts (Line 45:11 - Line 59:11), packages/shared/src/utils/geometry/geometryUtils.ts (Line 75:3 - Line 89:3)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn18" onclick="toggleCodeBlock('cloneGroup18', 'expandBtn18', 'collapseBtn18')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn18" onclick="toggleCodeBlock('cloneGroup18', 'expandBtn18', 'collapseBtn18')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup18"><code class="language-typescript text-sm text-gray-800">.y;

  const determinant = a1 * b2 - a2 * b1;

  if (determinant === 0) {
    // Lines are parallel
    return null;
  }

  const x = (b2 * c1 - b1 * c2) / determinant;
  const y = (a1 * c2 - a2 * c1) / determinant;

  // Check if the intersection point is on both line segments
  const onSegment1 =
    Math.min(line1Start</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/shared/utils/polygonSlicingUtils.ts (Line 76:1 - Line 126:4), packages/shared/src/utils/geometry/geometryUtils.ts (Line 139:1 - Line 190:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn19" onclick="toggleCodeBlock('cloneGroup19', 'expandBtn19', 'collapseBtn19')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn19" onclick="toggleCodeBlock('cloneGroup19', 'expandBtn19', 'collapseBtn19')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup19"><code class="language-typescript text-sm text-gray-800">export const distanceToLineSegment = (
  point: Point,
  lineStart: Point,
  lineEnd: Point
): number =&gt; {
  const dx = lineEnd.x - lineStart.x;
  const dy = lineEnd.y - lineStart.y;
  
  // Line length squared
  const lineLengthSquared = dx * dx + dy * dy;
  
  if (lineLengthSquared === 0) {
    // Line is actually a point
    return Math.sqrt(
      Math.pow(point.x - lineStart.x, 2) + 
      Math.pow(point.y - lineStart.y, 2)
    );
  }
  
  // Calculate the projection of the point onto the line
  const t = ((point.x - lineStart.x) * dx + (point.y - lineStart.y) * dy) / lineLengthSquared;
  
  if (t &lt; 0) {
    // Point is beyond the lineStart end of the line segment
    return Math.sqrt(
      Math.pow(point.x - lineStart.x, 2) + 
      Math.pow(point.y - lineStart.y, 2)
    );
  }
  
  if (t &gt; 1) {
    // Point is beyond the lineEnd end of the line segment
    return Math.sqrt(
      Math.pow(point.x - lineEnd.x, 2) + 
      Math.pow(point.y - lineEnd.y, 2)
    );
  }
  
  // Projection falls on the line segment
  const projectionX = lineStart.x + t * dx;
  const projectionY = lineStart.y + t * dy;
  
  return Math.sqrt(
    Math.pow(point.x - projectionX, 2) + 
    Math.pow(point.y - projectionY, 2)
  );
};

/**
 * Create a new polygon with the given points and type
 */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/shared/utils/polygonSlicingUtils.ts (Line 151:19 - Line 167:2), packages/shared/src/utils/geometry/slicingUtils.ts (Line 123:13 - Line 139:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn20" onclick="toggleCodeBlock('cloneGroup20', 'expandBtn20', 'collapseBtn20')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn20" onclick="toggleCodeBlock('cloneGroup20', 'expandBtn20', 'collapseBtn20')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup20"><code class="language-typescript text-sm text-gray-800">(polygon.points, sliceStart, sliceEnd);
    
    if (!result || result.length &lt; 2) {
      return null;
    }
    
    // Create new polygon objects from the result
    return result.map(points =&gt; ({
      id: uuidv4(),
      points,
      type: polygon.type || 'external'
    }));
  } catch (error) {
    console.error('Error in slicePolygon:', error);
    return null;
  }
};</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/shared/utils/polygonOperationsUtils.ts (Line 29:7 - Line 154:4), packages/shared/src/utils/geometry/geometryUtils.ts (Line 13:7 - Line 138:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn21" onclick="toggleCodeBlock('cloneGroup21', 'expandBtn21', 'collapseBtn21')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn21" onclick="toggleCodeBlock('cloneGroup21', 'expandBtn21', 'collapseBtn21')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup21"><code class="language-typescript text-sm text-gray-800">;
}

/**
 * Calculate the bounding box of a polygon
 */
export const calculateBoundingBox = (points: Point[]): BoundingBox =&gt; {
  if (!points.length) {
    return { minX: 0, minY: 0, maxX: 0, maxY: 0 };
  }

  let minX = Infinity;
  let minY = Infinity;
  let maxX = -Infinity;
  let maxY = -Infinity;

  for (const point of points) {
    minX = Math.min(minX, point.x);
    minY = Math.min(minY, point.y);
    maxX = Math.max(maxX, point.x);
    maxY = Math.max(maxY, point.y);
  }

  return { minX, minY, maxX, maxY };
};

/**
 * Check if a point is inside a polygon using ray casting algorithm
 */
export const isPointInPolygon = (point: Point, polygon: Point[]): boolean =&gt; {
  if (polygon.length &lt; 3) return false;

  let inside = false;
  for (let i = 0, j = polygon.length - 1; i &lt; polygon.length; j = i++) {
    const xi = polygon[i].x;
    const yi = polygon[i].y;
    const xj = polygon[j].x;
    const yj = polygon[j].y;

    const intersect = ((yi &gt; point.y) !== (yj &gt; point.y)) &amp;&amp;
      (point.x &lt; (xj - xi) * (point.y - yi) / (yj - yi) + xi);

    if (intersect) inside = !inside;
  }

  return inside;
};

/**
 * Calculate the intersection point of two line segments
 */
export const calculateIntersection = (
  p1: Point, p2: Point, p3: Point, p4: Point
): Point | null =&gt; {
  // Line 1 represented as a1x + b1y = c1
  const a1 = p2.y - p1.y;
  const b1 = p1.x - p2.x;
  const c1 = a1 * p1.x + b1 * p1.y;

  // Line 2 represented as a2x + b2y = c2
  const a2 = p4.y - p3.y;
  const b2 = p3.x - p4.x;
  const c2 = a2 * p3.x + b2 * p3.y;

  const determinant = a1 * b2 - a2 * b1;

  if (determinant === 0) {
    // Lines are parallel
    return null;
  }

  const x = (b2 * c1 - b1 * c2) / determinant;
  const y = (a1 * c2 - a2 * c1) / determinant;

  // Check if the intersection point is on both line segments
  const onSegment1 =
    Math.min(p1.x, p2.x) &lt;= x &amp;&amp; x &lt;= Math.max(p1.x, p2.x) &amp;&amp;
    Math.min(p1.y, p2.y) &lt;= y &amp;&amp; y &lt;= Math.max(p1.y, p2.y);

  const onSegment2 =
    Math.min(p3.x, p4.x) &lt;= x &amp;&amp; x &lt;= Math.max(p3.x, p4.x) &amp;&amp;
    Math.min(p3.y, p4.y) &lt;= y &amp;&amp; y &lt;= Math.max(p3.y, p4.y);

  if (onSegment1 &amp;&amp; onSegment2) {
    return { x, y };
  }

  return null;
};

/**
 * Calculate all intersection points between a line and a polygon
 */
export const calculateLinePolygonIntersections = (
  lineStart: Point,
  lineEnd: Point,
  polygon: Point[]
): Point[] =&gt; {
  const intersections: Point[] = [];

  for (let i = 0; i &lt; polygon.length; i++) {
    const j = (i + 1) % polygon.length;
    const intersection = calculateIntersection(
      lineStart, lineEnd, polygon[i], polygon[j]
    );

    if (intersection) {
      // Add a small epsilon to avoid duplicate points
      const epsilon = 0.0001;
      const isDuplicate = intersections.some(p =&gt;
        Math.abs(p.x - intersection.x) &lt; epsilon &amp;&amp;
        Math.abs(p.y - intersection.y) &lt; epsilon
      );

      if (!isDuplicate) {
        intersections.push(intersection);
      }
    }
  }

  return intersections;
};

/**
 * Calculate perpendicular distance from a point to a line
 */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/shared/utils/polygonOperationsUtils.ts (Line 164:3 - Line 205:4), packages/shared/src/utils/geometry/geometryUtils.ts (Line 148:3 - Line 190:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn22" onclick="toggleCodeBlock('cloneGroup22', 'expandBtn22', 'collapseBtn22')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn22" onclick="toggleCodeBlock('cloneGroup22', 'expandBtn22', 'collapseBtn22')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup22"><code class="language-typescript text-sm text-gray-800">const lineLengthSquared = dx * dx + dy * dy;

  if (lineLengthSquared === 0) {
    // Line is actually a point
    return Math.sqrt(
      Math.pow(point.x - lineStart.x, 2) +
      Math.pow(point.y - lineStart.y, 2)
    );
  }

  // Calculate the projection of the point onto the line
  const t = ((point.x - lineStart.x) * dx + (point.y - lineStart.y) * dy) / lineLengthSquared;

  if (t &lt; 0) {
    // Point is beyond the lineStart end of the line segment
    return Math.sqrt(
      Math.pow(point.x - lineStart.x, 2) +
      Math.pow(point.y - lineStart.y, 2)
    );
  }

  if (t &gt; 1) {
    // Point is beyond the lineEnd end of the line segment
    return Math.sqrt(
      Math.pow(point.x - lineEnd.x, 2) +
      Math.pow(point.y - lineEnd.y, 2)
    );
  }

  // Projection falls on the line segment
  const projectionX = lineStart.x + t * dx;
  const projectionY = lineStart.y + t * dy;

  return Math.sqrt(
    Math.pow(point.x - projectionX, 2) +
    Math.pow(point.y - projectionY, 2)
  );
};

/**
 * Slice a polygon with a line
 */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/shared/utils/polygonOperationsUtils.ts (Line 203:1 - Line 285:4), packages/shared/src/utils/geometry/slicingUtils.ts (Line 20:1 - Line 102:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn23" onclick="toggleCodeBlock('cloneGroup23', 'expandBtn23', 'collapseBtn23')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn23" onclick="toggleCodeBlock('cloneGroup23', 'expandBtn23', 'collapseBtn23')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup23"><code class="language-typescript text-sm text-gray-800">/**
 * Slice a polygon with a line
 */
export const slicePolygon = (
  polygon: Point[],
  sliceStart: Point,
  sliceEnd: Point
): Point[][] =&gt; {
  // Find intersections
  const intersections = calculateLinePolygonIntersections(
    sliceStart, sliceEnd, polygon
  );

  // If we don't have exactly 2 intersections, we can't slice properly
  if (intersections.length !== 2) {
    return [polygon]; // Return original polygon
  }

  // Sort intersections by distance from slice start
  intersections.sort((a, b) =&gt; {
    const distA = Math.pow(a.x - sliceStart.x, 2) + Math.pow(a.y - sliceStart.y, 2);
    const distB = Math.pow(b.x - sliceStart.x, 2) + Math.pow(b.y - sliceStart.y, 2);
    return distA - distB;
  });

  // Find the polygon edges that contain the intersections
  const intersectionEdges: number[] = [];

  for (const intersection of intersections) {
    for (let i = 0; i &lt; polygon.length; i++) {
      const j = (i + 1) % polygon.length;
      const p1 = polygon[i];
      const p2 = polygon[j];

      const intersection2 = calculateIntersection(
        sliceStart, sliceEnd, p1, p2
      );

      if (intersection2) {
        const epsilon = 0.0001;
        if (
          Math.abs(intersection.x - intersection2.x) &lt; epsilon &amp;&amp;
          Math.abs(intersection.y - intersection2.y) &lt; epsilon
        ) {
          intersectionEdges.push(i);
          break;
        }
      }
    }
  }

  // Create two new polygons
  const poly1: Point[] = [];
  const poly2: Point[] = [];

  // First polygon: from edge1 to edge2 via one side
  let currentIndex = intersectionEdges[0];
  poly1.push(intersections[0]);

  while (currentIndex !== intersectionEdges[1]) {
    currentIndex = (currentIndex + 1) % polygon.length;
    poly1.push(polygon[currentIndex]);
  }

  poly1.push(intersections[1]);

  // Second polygon: from edge2 to edge1 via the other side
  currentIndex = intersectionEdges[1];
  poly2.push(intersections[1]);

  while (currentIndex !== intersectionEdges[0]) {
    currentIndex = (currentIndex + 1) % polygon.length;
    poly2.push(polygon[currentIndex]);
  }

  poly2.push(intersections[0]);

  return [poly1, poly2];
};

/**
 * Calculate polygon area using the Shoelace formula
 */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/shared/utils/polygonOperationsUtils.ts (Line 280:2 - Line 317:4), packages/shared/src/utils/geometry/geometryUtils.ts (Line 199:2 - Line 236:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn24" onclick="toggleCodeBlock('cloneGroup24', 'expandBtn24', 'collapseBtn24')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn24" onclick="toggleCodeBlock('cloneGroup24', 'expandBtn24', 'collapseBtn24')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup24"><code class="language-typescript text-sm text-gray-800">;
};

/**
 * Calculate polygon area using the Shoelace formula
 */
export const calculatePolygonArea = (points: Point[]): number =&gt; {
  let area = 0;

  for (let i = 0; i &lt; points.length; i++) {
    const j = (i + 1) % points.length;
    area += points[i].x * points[j].y;
    area -= points[j].x * points[i].y;
  }

  return Math.abs(area / 2);
};

/**
 * Calculate polygon perimeter
 */
export const calculatePolygonPerimeter = (points: Point[]): number =&gt; {
  let perimeter = 0;

  for (let i = 0; i &lt; points.length; i++) {
    const j = (i + 1) % points.length;
    perimeter += Math.sqrt(
      Math.pow(points[j].x - points[i].x, 2) +
      Math.pow(points[j].y - points[i].y, 2)
    );
  }

  return perimeter;
};

/**
 * Simplify a polygon using the Ramer-Douglas-Peucker algorithm
 */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/shared/utils/polygonOperationsUtils.ts (Line 313:2 - Line 332:22), packages/shared/src/utils/geometry/geometryUtils.ts (Line 321:2 - Line 340:22)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn25" onclick="toggleCodeBlock('cloneGroup25', 'expandBtn25', 'collapseBtn25')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn25" onclick="toggleCodeBlock('cloneGroup25', 'expandBtn25', 'collapseBtn25')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup25"><code class="language-typescript text-sm text-gray-800">;

/**
 * Simplify a polygon using the Ramer-Douglas-Peucker algorithm
 */
export const simplifyPolygon = (
  points: Point[],
  epsilon: number
): Point[] =&gt; {
  if (points.length &lt;= 2) return points;

  // Find the point with the maximum distance
  let maxDistance = 0;
  let index = 0;

  const firstPoint = points[0];
  const lastPoint = points[points.length - 1];

  for (let i = 1; i &lt; points.length - 1; i++) {
    const distance = perpendicularDistance</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/shared/utils/polygonOperationsUtils.ts (Line 332:22 - Line 352:2), packages/shared/src/utils/geometry/geometryUtils.ts (Line 340:22 - Line 360:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn26" onclick="toggleCodeBlock('cloneGroup26', 'expandBtn26', 'collapseBtn26')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn26" onclick="toggleCodeBlock('cloneGroup26', 'expandBtn26', 'collapseBtn26')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup26"><code class="language-typescript text-sm text-gray-800">(points[i], firstPoint, lastPoint);

    if (distance &gt; maxDistance) {
      maxDistance = distance;
      index = i;
    }
  }

  // If max distance is greater than epsilon, recursively simplify
  if (maxDistance &gt; epsilon) {
    // Recursive call
    const firstHalf = simplifyPolygon(points.slice(0, index + 1), epsilon);
    const secondHalf = simplifyPolygon(points.slice(index), epsilon);

    // Concatenate the two parts
    return firstHalf.slice(0, -1).concat(secondHalf);
  } else {
    // Base case - return just the endpoints
    return [firstPoint, lastPoint];
  }
};</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/shared/utils/polygonOperationsUtils.ts (Line 352:1 - Line 419:2), packages/shared/src/utils/geometry/geometryUtils.ts (Line 254:1 - Line 321:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn27" onclick="toggleCodeBlock('cloneGroup27', 'expandBtn27', 'collapseBtn27')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn27" onclick="toggleCodeBlock('cloneGroup27', 'expandBtn27', 'collapseBtn27')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup27"><code class="language-typescript text-sm text-gray-800">};

/**
 * Check if a bounding box is visible in the viewport
 * Adds a margin to ensure polygons that are partially visible are included
 */
export const isBoxVisible = (
  box: BoundingBox,
  viewport: BoundingBox,
  margin: number = 100
): boolean =&gt; {
  // Add margin to viewport
  const viewportWithMargin = {
    minX: viewport.minX - margin,
    minY: viewport.minY - margin,
    maxX: viewport.maxX + margin,
    maxY: viewport.maxY + margin
  };

  // Check if the boxes overlap
  return !(
    box.maxX &lt; viewportWithMargin.minX ||
    box.minX &gt; viewportWithMargin.maxX ||
    box.maxY &lt; viewportWithMargin.minY ||
    box.minY &gt; viewportWithMargin.maxY
  );
};

/**
 * Memoize bounding box calculations for polygons
 */
export class PolygonBoundingBoxCache {
  private cache: Map&lt;string, BoundingBox&gt; = new Map();

  /**
   * Get the bounding box for a polygon, calculating it if not cached
   */
  getBoundingBox(polygonId: string, points: Point[]): BoundingBox {
    if (!this.cache.has(polygonId)) {
      this.cache.set(polygonId, calculateBoundingBox(points));
    }
    return this.cache.get(polygonId)!;
  }

  /**
   * Invalidate the cache for a specific polygon
   */
  invalidate(polygonId: string): void {
    this.cache.delete(polygonId);
  }

  /**
   * Clear the entire cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Get the number of cached bounding boxes
   */
  size(): number {
    return this.cache.size;
  }
}

// Create a singleton instance
export const polygonBoundingBoxCache = new PolygonBoundingBoxCache();</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/lib/segmentation/dynamicSimplification.ts (Line 127:2 - Line 137:9), packages/frontend/src/shared/utils/polygonOperationsUtils.ts (Line 326:2 - Line 336:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn29" onclick="toggleCodeBlock('cloneGroup29', 'expandBtn29', 'collapseBtn29')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn29" onclick="toggleCodeBlock('cloneGroup29', 'expandBtn29', 'collapseBtn29')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup29"><code class="language-typescript text-sm text-gray-800">= 0;

  const firstPoint = points[0];
  const lastPoint = points[points.length - 1];

  for (let i = 1; i &lt; points.length - 1; i++) {
    const distance = perpendicularDistance(points[i], firstPoint, lastPoint);

    if (distance &gt; maxDistance) {
      maxDistance = distance;
      maxIndex</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/lib/__mocks__/apiClient.ts (Line 48:3 - Line 75:2), packages/frontend/src/lib/__mocks__/apiClient.ts (Line 11:1 - Line 39:19)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn30" onclick="toggleCodeBlock('cloneGroup30', 'expandBtn30', 'collapseBtn30')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn30" onclick="toggleCodeBlock('cloneGroup30', 'expandBtn30', 'collapseBtn30')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup30"><code class="language-typescript text-sm text-gray-800">mockGet.mockImplementation((url) =&gt; {
    if (url.includes('/queue-status/project-123')) {
      return Promise.resolve({
        data: {
          queueLength: 1,
          runningTasks: ['task-1'],
          queuedTasks: ['task-2'],
          processingImages: [
            { id: 'task-1', name: 'Image 1', projectId: 'project-123' }
          ]
        }
      });
    } else if (url.includes('/queue-status')) {
      return Promise.resolve({
        data: {
          queueLength: 2,
          runningTasks: ['task-1', 'task-3'],
          queuedTasks: ['task-2', 'task-4'],
          processingImages: [
            { id: 'task-1', name: 'Image 1', projectId: 'project-123' },
            { id: 'task-3', name: 'Image 3', projectId: 'project-456' }
          ]
        }
      });
    }
    return Promise.reject(new Error('Not found'));
  });
}</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/__tests__/visual-regression/setupVisualRegression.ts (Line 69:8 - Line 89:6), packages/frontend/src/__tests__/visual-regression/setupVisualRegression.ts (Line 42:2 - Line 62:46)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn46" onclick="toggleCodeBlock('cloneGroup46', 'expandBtn46', 'collapseBtn46')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn46" onclick="toggleCodeBlock('cloneGroup46', 'expandBtn46', 'collapseBtn46')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup46"><code class="language-typescript text-sm text-gray-800">);
      
      // Construct snapshot path
      const snapshotPath = path.join(visualConfig.snapshotsDir, `${name}.png`);
      
      // If snapshot doesn't exist yet, save it
      if (!fs.existsSync(snapshotPath) &amp;&amp; process.env.UPDATE_VISUAL_SNAPSHOTS) {
        fs.writeFileSync(snapshotPath, screenshot);
        console.log(`Created new snapshot: ${name}`);
        return;
      }
      
      // Compare with existing snapshot
      expect(screenshot).toMatchImageSnapshot({
        customSnapshotIdentifier: name,
        customSnapshotsDir: visualConfig.snapshotsDir,
        ...visualConfig.comparisonOptions
      });
    };
    
    await</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/__tests__/fixtures/polygonFixtures.ts (Line 99:2 - Line 118:2), packages/frontend/src/__tests__/visual-regression/segmentation.visual.spec.ts (Line 69:2 - Line 89:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn47" onclick="toggleCodeBlock('cloneGroup47', 'expandBtn47', 'collapseBtn47')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn47" onclick="toggleCodeBlock('cloneGroup47', 'expandBtn47', 'collapseBtn47')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup47"><code class="language-typescript text-sm text-gray-800">[
    {
      points: [
        { x: 100, y: 100 },
        { x: 200, y: 100 },
        { x: 200, y: 200 },
        { x: 100, y: 200 }
      ],
      closed: true,
      color: '#FF0000'
    },
    {
      points: [
        { x: 300, y: 300 },
        { x: 400, y: 300 },
        { x: 350, y: 400 }
      ],
      closed: true,
      color: '#00FF00'
    },</code></pre></div><div class="py-4"><p class="text-gray-600">packages/shared/src/utils/polygonWorkerUtils.ts (Line 1:1 - Line 11:2), packages/frontend/src/shared/utils/polygonWorkerUtils.ts (Line 1:1 - Line 11:17)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn48" onclick="toggleCodeBlock('cloneGroup48', 'expandBtn48', 'collapseBtn48')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn48" onclick="toggleCodeBlock('cloneGroup48', 'expandBtn48', 'collapseBtn48')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup48"><code class="language-typescript text-sm text-gray-800">import { Point } from '@spheroseg/types';

/**
 * Interface for the polygon worker
 */
export interface PolygonWorker {
  isReady: boolean;
  calculatePolygonArea: (points: Point[]) =&gt; Promise&lt;number&gt;;
  calculatePolygonPerimeter: (points: Point[]) =&gt; Promise&lt;number&gt;;
  calculateBoundingBox: (points: Point[]) =&gt; Promise&lt;{ x: number; y: number; width: number; height: number } | null&gt;;
}</code></pre></div><div class="py-4"><p class="text-gray-600">packages/shared/src/utils/polygonWorkerUtils.ts (Line 10:5 - Line 82:2), packages/frontend/src/shared/utils/polygonWorkerUtils.ts (Line 13:2 - Line 85:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn49" onclick="toggleCodeBlock('cloneGroup49', 'expandBtn49', 'collapseBtn49')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn49" onclick="toggleCodeBlock('cloneGroup49', 'expandBtn49', 'collapseBtn49')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup49"><code class="language-typescript text-sm text-gray-800">&gt;;
}

/**
 * Base function to execute polygon worker operations with error handling
 */
export const executePolygonWorkerOperation = async &lt;T&gt;(
  points: Point[],
  polygonWorker: PolygonWorker,
  operation: (points: Point[]) =&gt; Promise&lt;T&gt;,
  operationName: string,
  defaultValue: T
): Promise&lt;T&gt; =&gt; {
  try {
    if (!polygonWorker.isReady) {
      console.warn(`Polygon worker not ready, returning ${defaultValue} for ${operationName}`);
      return defaultValue;
    }
    
    return await operation(points);
  } catch (error) {
    console.error(`Error in ${operationName}:`, error);
    return defaultValue;
  }
};

/**
 * Calculate polygon area using WebWorker
 */
export const calculatePolygonAreaAsync = async (
  points: Point[],
  polygonWorker: PolygonWorker
): Promise&lt;number&gt; =&gt; {
  return executePolygonWorkerOperation(
    points,
    polygonWorker,
    (pts) =&gt; polygonWorker.calculatePolygonArea(pts),
    'calculatePolygonAreaAsync',
    0
  );
};

/**
 * Calculate polygon perimeter using WebWorker
 */
export const calculatePolygonPerimeterAsync = async (
  points: Point[],
  polygonWorker: PolygonWorker
): Promise&lt;number&gt; =&gt; {
  return executePolygonWorkerOperation(
    points,
    polygonWorker,
    (pts) =&gt; polygonWorker.calculatePolygonPerimeter(pts),
    'calculatePolygonPerimeterAsync',
    0
  );
};

/**
 * Calculate polygon bounding box using WebWorker
 */
export const calculateBoundingBoxAsync = async (
  points: Point[],
  polygonWorker: PolygonWorker
): Promise&lt;{ x: number; y: number; width: number; height: number } | null&gt; =&gt; {
  return executePolygonWorkerOperation(
    points,
    polygonWorker,
    (pts) =&gt; polygonWorker.calculateBoundingBox(pts),
    'calculateBoundingBoxAsync',
    null
  );
};</code></pre></div><div class="py-4"><p class="text-gray-600">packages/shared/src/utils/polygonSlicingUtils.ts (Line 137:7 - Line 142:56), packages/shared/src/utils/geometry/slicingUtils.ts (Line 40:5 - Line 45:57)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn50" onclick="toggleCodeBlock('cloneGroup50', 'expandBtn50', 'collapseBtn50')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn50" onclick="toggleCodeBlock('cloneGroup50', 'expandBtn50', 'collapseBtn50')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup50"><code class="language-typescript text-sm text-gray-800">const distA = Math.pow(a.x - sliceStart.x, 2) + Math.pow(a.y - sliceStart.y, 2);
      const distB = Math.pow(b.x - sliceStart.x, 2) + Math.pow(b.y - sliceStart.y, 2);
      return distA - distB;
    });

    // Use the first and last intersections for a clean cut</code></pre></div><div class="py-4"><p class="text-gray-600">packages/shared/src/utils/polygonOperationsUtils.ts (Line 4:2 - Line 419:2), packages/frontend/src/shared/utils/polygonOperationsUtils.ts (Line 4:2 - Line 321:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn51" onclick="toggleCodeBlock('cloneGroup51', 'expandBtn51', 'collapseBtn51')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn51" onclick="toggleCodeBlock('cloneGroup51', 'expandBtn51', 'collapseBtn51')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup51"><code class="language-typescript text-sm text-gray-800">} from '@spheroseg/types';

/**
 * Interface representing a bounding box
 */
export interface BoundingBox {
  minX: number;
  minY: number;
  maxX: number;
  maxY: number;
}

/**
 * Worker message types
 */
export interface WorkerRequest {
  id: string;
  operation: string;
  data: any;
}

export interface WorkerResponse {
  id: string;
  operation: string;
  result: any;
  error?: string;
}

/**
 * Calculate the bounding box of a polygon
 */
export const calculateBoundingBox = (points: Point[]): BoundingBox =&gt; {
  if (!points.length) {
    return { minX: 0, minY: 0, maxX: 0, maxY: 0 };
  }

  let minX = Infinity;
  let minY = Infinity;
  let maxX = -Infinity;
  let maxY = -Infinity;

  for (const point of points) {
    minX = Math.min(minX, point.x);
    minY = Math.min(minY, point.y);
    maxX = Math.max(maxX, point.x);
    maxY = Math.max(maxY, point.y);
  }

  return { minX, minY, maxX, maxY };
};

/**
 * Check if a point is inside a polygon using ray casting algorithm
 */
export const isPointInPolygon = (point: Point, polygon: Point[]): boolean =&gt; {
  if (polygon.length &lt; 3) return false;

  let inside = false;
  for (let i = 0, j = polygon.length - 1; i &lt; polygon.length; j = i++) {
    const xi = polygon[i].x;
    const yi = polygon[i].y;
    const xj = polygon[j].x;
    const yj = polygon[j].y;

    const intersect = ((yi &gt; point.y) !== (yj &gt; point.y)) &amp;&amp;
      (point.x &lt; (xj - xi) * (point.y - yi) / (yj - yi) + xi);

    if (intersect) inside = !inside;
  }

  return inside;
};

/**
 * Calculate the intersection point of two line segments
 */
export const calculateIntersection = (
  p1: Point, p2: Point, p3: Point, p4: Point
): Point | null =&gt; {
  // Line 1 represented as a1x + b1y = c1
  const a1 = p2.y - p1.y;
  const b1 = p1.x - p2.x;
  const c1 = a1 * p1.x + b1 * p1.y;

  // Line 2 represented as a2x + b2y = c2
  const a2 = p4.y - p3.y;
  const b2 = p3.x - p4.x;
  const c2 = a2 * p3.x + b2 * p3.y;

  const determinant = a1 * b2 - a2 * b1;

  if (determinant === 0) {
    // Lines are parallel
    return null;
  }

  const x = (b2 * c1 - b1 * c2) / determinant;
  const y = (a1 * c2 - a2 * c1) / determinant;

  // Check if the intersection point is on both line segments
  const onSegment1 = 
    Math.min(p1.x, p2.x) &lt;= x &amp;&amp; x &lt;= Math.max(p1.x, p2.x) &amp;&amp;
    Math.min(p1.y, p2.y) &lt;= y &amp;&amp; y &lt;= Math.max(p1.y, p2.y);
  
  const onSegment2 = 
    Math.min(p3.x, p4.x) &lt;= x &amp;&amp; x &lt;= Math.max(p3.x, p4.x) &amp;&amp;
    Math.min(p3.y, p4.y) &lt;= y &amp;&amp; y &lt;= Math.max(p3.y, p4.y);

  if (onSegment1 &amp;&amp; onSegment2) {
    return { x, y };
  }

  return null;
};

/**
 * Calculate all intersection points between a line and a polygon
 */
export const calculateLinePolygonIntersections = (
  lineStart: Point, 
  lineEnd: Point, 
  polygon: Point[]
): Point[] =&gt; {
  const intersections: Point[] = [];

  for (let i = 0; i &lt; polygon.length; i++) {
    const j = (i + 1) % polygon.length;
    const intersection = calculateIntersection(
      lineStart, lineEnd, polygon[i], polygon[j]
    );

    if (intersection) {
      // Add a small epsilon to avoid duplicate points
      const epsilon = 0.0001;
      const isDuplicate = intersections.some(p =&gt; 
        Math.abs(p.x - intersection.x) &lt; epsilon &amp;&amp; 
        Math.abs(p.y - intersection.y) &lt; epsilon
      );

      if (!isDuplicate) {
        intersections.push(intersection);
      }
    }
  }

  return intersections;
};

/**
 * Calculate perpendicular distance from a point to a line
 */
export const perpendicularDistance = (
  point: Point, 
  lineStart: Point, 
  lineEnd: Point
): number =&gt; {
  const dx = lineEnd.x - lineStart.x;
  const dy = lineEnd.y - lineStart.y;
  
  // Line length
  const lineLengthSquared = dx * dx + dy * dy;
  
  if (lineLengthSquared === 0) {
    // Line is actually a point
    return Math.sqrt(
      Math.pow(point.x - lineStart.x, 2) + 
      Math.pow(point.y - lineStart.y, 2)
    );
  }
  
  // Calculate the projection of the point onto the line
  const t = ((point.x - lineStart.x) * dx + (point.y - lineStart.y) * dy) / lineLengthSquared;
  
  if (t &lt; 0) {
    // Point is beyond the lineStart end of the line segment
    return Math.sqrt(
      Math.pow(point.x - lineStart.x, 2) + 
      Math.pow(point.y - lineStart.y, 2)
    );
  }
  
  if (t &gt; 1) {
    // Point is beyond the lineEnd end of the line segment
    return Math.sqrt(
      Math.pow(point.x - lineEnd.x, 2) + 
      Math.pow(point.y - lineEnd.y, 2)
    );
  }
  
  // Projection falls on the line segment
  const projectionX = lineStart.x + t * dx;
  const projectionY = lineStart.y + t * dy;
  
  return Math.sqrt(
    Math.pow(point.x - projectionX, 2) + 
    Math.pow(point.y - projectionY, 2)
  );
};

/**
 * Slice a polygon with a line
 */
export const slicePolygon = (
  polygon: Point[], 
  sliceStart: Point, 
  sliceEnd: Point
): Point[][] =&gt; {
  // Find intersections
  const intersections = calculateLinePolygonIntersections(
    sliceStart, sliceEnd, polygon
  );

  // If we don't have exactly 2 intersections, we can't slice properly
  if (intersections.length !== 2) {
    return [polygon]; // Return original polygon
  }

  // Sort intersections by distance from slice start
  intersections.sort((a, b) =&gt; {
    const distA = Math.pow(a.x - sliceStart.x, 2) + Math.pow(a.y - sliceStart.y, 2);
    const distB = Math.pow(b.x - sliceStart.x, 2) + Math.pow(b.y - sliceStart.y, 2);
    return distA - distB;
  });

  // Find the polygon edges that contain the intersections
  const intersectionEdges: number[] = [];
  
  for (const intersection of intersections) {
    for (let i = 0; i &lt; polygon.length; i++) {
      const j = (i + 1) % polygon.length;
      const p1 = polygon[i];
      const p2 = polygon[j];
      
      const intersection2 = calculateIntersection(
        sliceStart, sliceEnd, p1, p2
      );
      
      if (intersection2) {
        const epsilon = 0.0001;
        if (
          Math.abs(intersection.x - intersection2.x) &lt; epsilon &amp;&amp; 
          Math.abs(intersection.y - intersection2.y) &lt; epsilon
        ) {
          intersectionEdges.push(i);
          break;
        }
      }
    }
  }

  // Create two new polygons
  const poly1: Point[] = [];
  const poly2: Point[] = [];

  // First polygon: from edge1 to edge2 via one side
  let currentIndex = intersectionEdges[0];
  poly1.push(intersections[0]);
  
  while (currentIndex !== intersectionEdges[1]) {
    currentIndex = (currentIndex + 1) % polygon.length;
    poly1.push(polygon[currentIndex]);
  }
  
  poly1.push(intersections[1]);

  // Second polygon: from edge2 to edge1 via the other side
  currentIndex = intersectionEdges[1];
  poly2.push(intersections[1]);
  
  while (currentIndex !== intersectionEdges[0]) {
    currentIndex = (currentIndex + 1) % polygon.length;
    poly2.push(polygon[currentIndex]);
  }
  
  poly2.push(intersections[0]);

  return [poly1, poly2];
};

/**
 * Calculate polygon area using the Shoelace formula
 */
export const calculatePolygonArea = (points: Point[]): number =&gt; {
  let area = 0;
  
  for (let i = 0; i &lt; points.length; i++) {
    const j = (i + 1) % points.length;
    area += points[i].x * points[j].y;
    area -= points[j].x * points[i].y;
  }
  
  return Math.abs(area / 2);
};

/**
 * Calculate polygon perimeter
 */
export const calculatePolygonPerimeter = (points: Point[]): number =&gt; {
  let perimeter = 0;
  
  for (let i = 0; i &lt; points.length; i++) {
    const j = (i + 1) % points.length;
    perimeter += Math.sqrt(
      Math.pow(points[j].x - points[i].x, 2) + 
      Math.pow(points[j].y - points[i].y, 2)
    );
  }
  
  return perimeter;
};

/**
 * Simplify a polygon using the Ramer-Douglas-Peucker algorithm
 */
export const simplifyPolygon = (
  points: Point[], 
  epsilon: number
): Point[] =&gt; {
  if (points.length &lt;= 2) return points;

  // Find the point with the maximum distance
  let maxDistance = 0;
  let index = 0;
  
  const firstPoint = points[0];
  const lastPoint = points[points.length - 1];
  
  for (let i = 1; i &lt; points.length - 1; i++) {
    const distance = perpendicularDistance(points[i], firstPoint, lastPoint);
    
    if (distance &gt; maxDistance) {
      maxDistance = distance;
      index = i;
    }
  }
  
  // If max distance is greater than epsilon, recursively simplify
  if (maxDistance &gt; epsilon) {
    // Recursive call
    const firstHalf = simplifyPolygon(points.slice(0, index + 1), epsilon);
    const secondHalf = simplifyPolygon(points.slice(index), epsilon);
    
    // Concatenate the two parts
    return firstHalf.slice(0, -1).concat(secondHalf);
  } else {
    // Base case - return just the endpoints
    return [firstPoint, lastPoint];
  }
};

/**
 * Check if a bounding box is visible in the viewport
 * Adds a margin to ensure polygons that are partially visible are included
 */
export const isBoxVisible = (
  box: BoundingBox,
  viewport: BoundingBox,
  margin: number = 100
): boolean =&gt; {
  // Add margin to viewport
  const viewportWithMargin = {
    minX: viewport.minX - margin,
    minY: viewport.minY - margin,
    maxX: viewport.maxX + margin,
    maxY: viewport.maxY + margin
  };

  // Check if the boxes overlap
  return !(
    box.maxX &lt; viewportWithMargin.minX ||
    box.minX &gt; viewportWithMargin.maxX ||
    box.maxY &lt; viewportWithMargin.minY ||
    box.minY &gt; viewportWithMargin.maxY
  );
};

/**
 * Memoize bounding box calculations for polygons
 */
export class PolygonBoundingBoxCache {
  private cache: Map&lt;string, BoundingBox&gt; = new Map();
  
  /**
   * Get the bounding box for a polygon, calculating it if not cached
   */
  getBoundingBox(polygonId: string, points: Point[]): BoundingBox {
    if (!this.cache.has(polygonId)) {
      this.cache.set(polygonId, calculateBoundingBox(points));
    }
    return this.cache.get(polygonId)!;
  }
  
  /**
   * Invalidate the cache for a specific polygon
   */
  invalidate(polygonId: string): void {
    this.cache.delete(polygonId);
  }
  
  /**
   * Clear the entire cache
   */
  clear(): void {
    this.cache.clear();
  }
  
  /**
   * Get the number of cached bounding boxes
   */
  size(): number {
    return this.cache.size;
  }
}

// Create a singleton instance
export const polygonBoundingBoxCache = new PolygonBoundingBoxCache();</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/utils/polygonUtils.ts (Line 80:2 - Line 100:4), packages/frontend/src/pages/segmentation/hooks/segmentation/geometry.worker.ts (Line 45:2 - Line 61:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn52" onclick="toggleCodeBlock('cloneGroup52', 'expandBtn52', 'collapseBtn52')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn52" onclick="toggleCodeBlock('cloneGroup52', 'expandBtn52', 'collapseBtn52')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup52"><code class="language-typescript text-sm text-gray-800">= (x: number, y: number, points: Point[]): boolean =&gt; {
  let inside = false;
  for (let i = 0, j = points.length - 1; i &lt; points.length; j = i++) {
    const xi = points[i].x;
    const yi = points[i].y;
    const xj = points[j].x;
    const yj = points[j].y;

    const intersect = ((yi &gt; y) !== (yj &gt; y)) &amp;&amp;
                      (x &lt; (xj - xi) * (y - yi) / (yj - yi) + xi);
    if (intersect) inside = !inside;
  }
  return inside;
};

/**
 * Check if a point is inside a polygon (alternative version taking a Point object)
 * @param point Point to check
 * @param polygon Array of points defining the polygon
 * @returns True if the point is inside the polygon, false otherwise
 */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/utils/polygonUtils.ts (Line 200:13 - Line 209:66), packages/shared/src/utils/geometry/geometryUtils.ts (Line 111:6 - Line 120:49)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn53" onclick="toggleCodeBlock('cloneGroup53', 'expandBtn53', 'collapseBtn53')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn53" onclick="toggleCodeBlock('cloneGroup53', 'expandBtn53', 'collapseBtn53')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup53"><code class="language-typescript text-sm text-gray-800">[] = [];

  for (let i = 0; i &lt; polygon.length; i++) {
    const j = (i + 1) % polygon.length;
    const intersection = calculateIntersection(
      lineStart, lineEnd, polygon[i], polygon[j]
    );

    if (intersection) {
      // Calculate the distance from the line start to the intersection</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/utils/polygonUtils.ts (Line 212:7 - Line 220:2), packages/shared/src/utils/geometry/geometryUtils.ts (Line 120:7 - Line 128:13)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn54" onclick="toggleCodeBlock('cloneGroup54', 'expandBtn54', 'collapseBtn54')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn54" onclick="toggleCodeBlock('cloneGroup54', 'expandBtn54', 'collapseBtn54')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup54"><code class="language-typescript text-sm text-gray-800">// Add a small epsilon to avoid duplicate points
      const epsilon = 0.0001;
      const isDuplicate = intersections.some(p =&gt;
        Math.abs(p.x - intersection.x) &lt; epsilon &amp;&amp;
        Math.abs(p.y - intersection.y) &lt; epsilon
      );

      if (!isDuplicate) {
        intersections.push({</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/utils/polygonUtils.ts (Line 404:1 - Line 417:4), packages/frontend/src/pages/segmentation/utils/geometry.ts (Line 50:1 - Line 61:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn55" onclick="toggleCodeBlock('cloneGroup55', 'expandBtn55', 'collapseBtn55')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn55" onclick="toggleCodeBlock('cloneGroup55', 'expandBtn55', 'collapseBtn55')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup55"><code class="language-typescript text-sm text-gray-800">export const isClockwise = (points: Point[]): boolean =&gt; {
  let sum = 0;
  for (let i = 0; i &lt; points.length; i++) {
    const j = (i + 1) % points.length;
    sum += (points[j].x - points[i].x) * (points[j].y + points[i].y);
  }
  return sum &gt; 0;
};

/**
 * Ensure a polygon is oriented clockwise
 * @param points Array of points defining the polygon
 * @returns Array of points with clockwise orientation
 */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/utils/polygonUtils.ts (Line 443:1 - Line 462:73), packages/shared/src/utils/geometry/geometryUtils.ts (Line 326:1 - Line 340:65)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn56" onclick="toggleCodeBlock('cloneGroup56', 'expandBtn56', 'collapseBtn56')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn56" onclick="toggleCodeBlock('cloneGroup56', 'expandBtn56', 'collapseBtn56')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup56"><code class="language-typescript text-sm text-gray-800">export const simplifyPolygon = (points: Point[], epsilon: number): Point[] =&gt; {
  if (points.length &lt;= 2) return points;

  // Find the point with the maximum distance
  let maxDistance = 0;
  let index = 0;

  const firstPoint = points[0];
  const lastPoint = points[points.length - 1];

  for (let i = 1; i &lt; points.length - 1; i++) {
    const distance = perpendicularDistance(points[i], firstPoint, lastPoint);

    if (distance &gt; maxDistance) {
      maxDistance = distance;
      index = i;
    }
  }

  // If the maximum distance is greater than epsilon, recursively simplify</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/utils/polygonUtils.ts (Line 551:3 - Line 579:2), packages/shared/src/utils/polygonSlicingUtils.ts (Line 147:5 - Line 175:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn57" onclick="toggleCodeBlock('cloneGroup57', 'expandBtn57', 'collapseBtn57')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn57" onclick="toggleCodeBlock('cloneGroup57', 'expandBtn57', 'collapseBtn57')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup57"><code class="language-typescript text-sm text-gray-800">const polygon1Points: Point[] = [];
  const polygon2Points: Point[] = [];

  // First polygon
  polygon1Points.push({ x: int1.x, y: int1.y });

  let i = (int1.edgeIndex + 1) % polygonPoints.length;
  while (i !== (int2.edgeIndex + 1) % polygonPoints.length) {
    polygon1Points.push({ ...polygonPoints[i] });
    i = (i + 1) % polygonPoints.length;
  }

  polygon1Points.push({ x: int2.x, y: int2.y });

  // Second polygon
  polygon2Points.push({ x: int2.x, y: int2.y });

  i = (int2.edgeIndex + 1) % polygonPoints.length;
  while (i !== (int1.edgeIndex + 1) % polygonPoints.length) {
    polygon2Points.push({ ...polygonPoints[i] });
    i = (i + 1) % polygonPoints.length;
  }

  polygon2Points.push({ x: int1.x, y: int1.y });

  // Ensure both polygons have at least 3 points
  if (polygon1Points.length &gt;= 3 &amp;&amp; polygon2Points.length &gt;= 3) {
    // Create new polygon objects
    const newPolygon1 =</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/utils/imageUtils.ts (Line 34:12 - Line 52:7), packages/frontend/src/pages/segmentation/utils/imageLoader.ts (Line 32:2 - Line 54:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn58" onclick="toggleCodeBlock('cloneGroup58', 'expandBtn58', 'collapseBtn58')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn58" onclick="toggleCodeBlock('cloneGroup58', 'expandBtn58', 'collapseBtn58')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup58"><code class="language-typescript text-sm text-gray-800">;

    img.onload = () =&gt; {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      });
    };

    img.onerror = () =&gt; {
      console.error(`Failed to load image from ${url}`);
      resolve(null);
    };

    img.src = url;
  });
};

export</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/utils/imageLoader.ts (Line 60:2 - Line 72:7), packages/frontend/src/utils/imageUtils.ts (Line 14:2 - Line 26:8)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn59" onclick="toggleCodeBlock('cloneGroup59', 'expandBtn59', 'collapseBtn59')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn59" onclick="toggleCodeBlock('cloneGroup59', 'expandBtn59', 'collapseBtn59')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup59"><code class="language-typescript text-sm text-gray-800">= async (url: string): Promise&lt;boolean&gt; =&gt; {
  try {
    const response = await fetch(url, {
      method: 'HEAD',
      cache: 'no-cache',
      headers: {
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    });
    return response.ok;
  } catch (error) {
    logger</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/services/authService.ts (Line 369:13 - Line 392:2), packages/frontend/src/services/authService.ts (Line 275:13 - Line 299:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn60" onclick="toggleCodeBlock('cloneGroup60', 'expandBtn60', 'collapseBtn60')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn60" onclick="toggleCodeBlock('cloneGroup60', 'expandBtn60', 'collapseBtn60')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup60"><code class="language-typescript text-sm text-gray-800">withCredentials: false
          });

          // Update tokens in localStorage
          const { accessToken, refreshToken } = response.data;
          setTokens(accessToken, refreshToken);

          // Set up cookie as well
          document.cookie = `auth_token=${accessToken}; path=/; samesite=strict; max-age=3600`;

          logger.info('[authService] Development mode login successful');
          return true;
        } finally {
          clearTimeout(timeoutId);
        }
      } catch (devError) {
        logger.error('[authService] Failed to perform development login:', devError);
        return false;
      }
    }

    return false;
  }
}</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/lib/svgUtils.ts (Line 61:3 - Line 71:2), packages/frontend/src/pages/segmentation/utils/geometry.ts (Line 7:5 - Line 17:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn83" onclick="toggleCodeBlock('cloneGroup83', 'expandBtn83', 'collapseBtn83')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn83" onclick="toggleCodeBlock('cloneGroup83', 'expandBtn83', 'collapseBtn83')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup83"><code class="language-typescript text-sm text-gray-800">let inside = false;
  for (let i = 0, j = polygon.length - 1; i &lt; polygon.length; j = i++) {
    const xi = polygon[i].x;
    const yi = polygon[i].y;
    const xj = polygon[j].x;
    const yj = polygon[j].y;
    
    const intersect = ((yi &gt; point.y) !== (yj &gt; point.y)) &amp;&amp;
      (point.x &lt; (xj - xi) * (point.y - yi) / (yj - yi) + xi);
    
    if (intersect) {</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/hooks/useRecentActivity.ts (Line 245:35 - Line 263:53), packages/frontend/src/hooks/useUserStatistics.ts (Line 273:33 - Line 291:53)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn84" onclick="toggleCodeBlock('cloneGroup84', 'expandBtn84', 'collapseBtn84')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn84" onclick="toggleCodeBlock('cloneGroup84', 'expandBtn84', 'collapseBtn84')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup84"><code class="language-typescript text-sm text-gray-800">;
      
      if (axios.isAxiosError(err) &amp;&amp; err.response) {
        errorMessage = err.response.data?.message || errorMessage;
      } else if (err instanceof Error) {
        errorMessage = err.message;
      }
      
      setError(errorMessage);
      
      if (showToasts) {
        toast.error(errorMessage);
      }
      
      // Try to use cached data even if it's expired
      if (useCache) {
        const { data: cachedData } = loadFromCache();
        if (cachedData) {
          logger.info('Using expired cached activities due to fetch error'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/hooks/useProjectDuplicate.ts (Line 263:30 - Line 277:7), packages/frontend/src/hooks/useUserStatistics.ts (Line 273:33 - Line 287:47)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn85" onclick="toggleCodeBlock('cloneGroup85', 'expandBtn85', 'collapseBtn85')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn85" onclick="toggleCodeBlock('cloneGroup85', 'expandBtn85', 'collapseBtn85')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup85"><code class="language-typescript text-sm text-gray-800">;
      
      if (axios.isAxiosError(err) &amp;&amp; err.response) {
        errorMessage = err.response.data?.message || errorMessage;
      } else if (err instanceof Error) {
        errorMessage = err.message;
      }
      
      setError(errorMessage);
      
      if (showToasts) {
        toast.error(errorMessage);
      }
      
      return</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/hooks/useImageResegment.ts (Line 156:35 - Line 170:35), packages/frontend/src/hooks/useUserStatistics.ts (Line 273:33 - Line 287:47)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn86" onclick="toggleCodeBlock('cloneGroup86', 'expandBtn86', 'collapseBtn86')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn86" onclick="toggleCodeBlock('cloneGroup86', 'expandBtn86', 'collapseBtn86')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup86"><code class="language-typescript text-sm text-gray-800">;
      
      if (axios.isAxiosError(err) &amp;&amp; err.response) {
        errorMessage = err.response.data?.message || errorMessage;
      } else if (err instanceof Error) {
        errorMessage = err.message;
      }
      
      setError(errorMessage);
      
      if (showToasts) {
        toast.error(errorMessage);
      }
      
      // Revert status if it was changed</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/hooks/useImageResegment.ts (Line 244:41 - Line 258:40), packages/frontend/src/hooks/useUserStatistics.ts (Line 273:33 - Line 287:47)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn87" onclick="toggleCodeBlock('cloneGroup87', 'expandBtn87', 'collapseBtn87')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn87" onclick="toggleCodeBlock('cloneGroup87', 'expandBtn87', 'collapseBtn87')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup87"><code class="language-typescript text-sm text-gray-800">;
      
      if (axios.isAxiosError(err) &amp;&amp; err.response) {
        errorMessage = err.response.data?.message || errorMessage;
      } else if (err instanceof Error) {
        errorMessage = err.message;
      }
      
      setError(errorMessage);
      
      if (showToasts) {
        toast.error(errorMessage);
      }
      
      // Revert statuses if they were changed</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/hooks/useImageDelete.ts (Line 121:25 - Line 135:6), packages/frontend/src/hooks/useUserStatistics.ts (Line 273:33 - Line 287:47)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn88" onclick="toggleCodeBlock('cloneGroup88', 'expandBtn88', 'collapseBtn88')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn88" onclick="toggleCodeBlock('cloneGroup88', 'expandBtn88', 'collapseBtn88')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup88"><code class="language-typescript text-sm text-gray-800">;
      
      if (axios.isAxiosError(err) &amp;&amp; err.response) {
        errorMessage = err.response.data?.message || errorMessage;
      } else if (err instanceof Error) {
        errorMessage = err.message;
      }
      
      setError(errorMessage);
      
      if (showToasts) {
        toast.error(errorMessage);
      }
      
      throw</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/hooks/useImageDelete.ts (Line 190:31 - Line 203:2), packages/frontend/src/hooks/useUserStatistics.ts (Line 273:33 - Line 287:47)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn89" onclick="toggleCodeBlock('cloneGroup89', 'expandBtn89', 'collapseBtn89')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn89" onclick="toggleCodeBlock('cloneGroup89', 'expandBtn89', 'collapseBtn89')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup89"><code class="language-typescript text-sm text-gray-800">;
      
      if (axios.isAxiosError(err) &amp;&amp; err.response) {
        errorMessage = err.response.data?.message || errorMessage;
      } else if (err instanceof Error) {
        errorMessage = err.message;
      }
      
      setError(errorMessage);
      
      if (showToasts) {
        toast.error(errorMessage);
      }
    }</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/api/projectImages.ts (Line 145:7 - Line 153:2), packages/frontend/src/api/projectImages.ts (Line 123:9 - Line 132:73)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn100" onclick="toggleCodeBlock('cloneGroup100', 'expandBtn100', 'collapseBtn100')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn100" onclick="toggleCodeBlock('cloneGroup100', 'expandBtn100', 'collapseBtn100')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup100"><code class="language-typescript text-sm text-gray-800">try {
        const legacyResponse = await fetch(`/api/images?projectId=${projectId}`);

        if (legacyResponse.ok) {
          const legacyImages = await legacyResponse.json();
          console.log(`Successfully fetched ${legacyImages.length} images from legacy API`);
          return legacyImages.map((image: Image) =&gt; mapApiImageToProjectImage(image));
        }
      }</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/test-utils/mockDatabase.ts (Line 290:17 - Line 310:20), packages/backend/src/test-utils/mockDatabase.ts (Line 218:46 - Line 238:20)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn101" onclick="toggleCodeBlock('cloneGroup101', 'expandBtn101', 'collapseBtn101')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn101" onclick="toggleCodeBlock('cloneGroup101', 'expandBtn101', 'collapseBtn101')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup101"><code class="language-typescript text-sm text-gray-800">);
      if (whereMatch) {
        const conditions = whereMatch[1];
        
        // Parse basic key-value conditions
        const conditionParts = conditions.split('AND').map(part =&gt; part.trim());
        conditionParts.forEach(condition =&gt; {
          const match = condition.match(/([^\s=&lt;&gt;]+)\s*(=|&lt;&gt;|&gt;|&lt;|&gt;=|&lt;=)\s*(?:'([^']*)'|(\d+)|(\$\d+))/);
          if (match) {
            const [, column, operator, stringValue, numberValue, placeholder] = match;
            
            // For placeholder values, we'll handle them separately
            if (!placeholder) {
              result.conditions[column] = stringValue || numberValue;
            }
          }
        });
      }
    }
    
    // Check for DELETE</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/test-utils/mockDatabase.ts (Line 317:2 - Line 341:7), packages/backend/src/test-utils/mockDatabase.ts (Line 286:2 - Line 238:20)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn102" onclick="toggleCodeBlock('cloneGroup102', 'expandBtn102', 'collapseBtn102')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn102" onclick="toggleCodeBlock('cloneGroup102', 'expandBtn102', 'collapseBtn102')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup102"><code class="language-typescript text-sm text-gray-800">;
      }
      
      // Extract WHERE conditions
      const whereMatch = queryText.match(/WHERE\s+(.+)$/i);
      if (whereMatch) {
        const conditions = whereMatch[1];
        
        // Parse basic key-value conditions
        const conditionParts = conditions.split('AND').map(part =&gt; part.trim());
        conditionParts.forEach(condition =&gt; {
          const match = condition.match(/([^\s=&lt;&gt;]+)\s*(=|&lt;&gt;|&gt;|&lt;|&gt;=|&lt;=)\s*(?:'([^']*)'|(\d+)|(\$\d+))/);
          if (match) {
            const [, column, operator, stringValue, numberValue, placeholder] = match;
            
            // For placeholder values, we'll handle them separately
            if (!placeholder) {
              result.conditions[column] = stringValue || numberValue;
            }
          }
        });
      }
    }
    
    return</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/test-utils/mockDatabase.ts (Line 430:11 - Line 444:2), packages/backend/src/test-utils/mockDatabase.ts (Line 379:11 - Line 394:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn103" onclick="toggleCodeBlock('cloneGroup103', 'expandBtn103', 'collapseBtn103')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn103" onclick="toggleCodeBlock('cloneGroup103', 'expandBtn103', 'collapseBtn103')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup103"><code class="language-typescript text-sm text-gray-800">const conditionParts = conditions.split('AND').map(part =&gt; part.trim());
          conditionParts.forEach((condition, index) =&gt; {
            const match = condition.match(/([^\s=&lt;&gt;]+)\s*(=|&lt;&gt;|&gt;|&lt;|&gt;=|&lt;=)\s*(\$\d+)/);
            if (match) {
              const [, column, operator, placeholder] = match;
              const paramIndex = parseInt(placeholder.substring(1)) - 1;
              
              if (paramIndex &gt;= 0 &amp;&amp; paramIndex &lt; values.length) {
                parsedQuery.conditions[column] = values[paramIndex];
              }
            }
          });
        }
      }
    }</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/test-utils/mockDatabase.ts (Line 500:2 - Line 506:15), packages/backend/src/test-utils/mockDatabase.ts (Line 453:2 - Line 459:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn104" onclick="toggleCodeBlock('cloneGroup104', 'expandBtn104', 'collapseBtn104')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn104" onclick="toggleCodeBlock('cloneGroup104', 'expandBtn104', 'collapseBtn104')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup104"><code class="language-typescript text-sm text-gray-800">= this.tables[parsedQuery.tableName].filter(row =&gt; {
          return Object.entries(parsedQuery.conditions).every(([column, value]) =&gt; {
            return row[column] === value;
          });
        });
        
        // Update rows</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/test-utils/mockAuth.ts (Line 224:7 - Line 235:10), packages/backend/src/test-utils/mockAuth.ts (Line 149:7 - Line 160:8)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn105" onclick="toggleCodeBlock('cloneGroup105', 'expandBtn105', 'collapseBtn105')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn105" onclick="toggleCodeBlock('cloneGroup105', 'expandBtn105', 'collapseBtn105')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup105"><code class="language-typescript text-sm text-gray-800">} else {
        const [, valueStr, unit] = match;
        const value = parseInt(valueStr, 10);
        
        switch (unit) {
          case 's': expiresAt = new Date(now.getTime() + value * 1000); break;
          case 'm': expiresAt = new Date(now.getTime() + value * 60000); break;
          case 'h': expiresAt = new Date(now.getTime() + value * 3600000); break;
          case 'd': expiresAt = new Date(now.getTime() + value * 86400000); break;
          case 'w': expiresAt = new Date(now.getTime() + value * 604800000); break;
          case 'y': expiresAt = new Date(now.getTime() + value * 31536000000); break;
          default: expiresAt = new Date(now.getTime() + 604800000</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/test-utils/mockAuth.ts (Line 319:2 - Line 339:6), packages/backend/src/test-utils/mockAuth.ts (Line 284:2 - Line 304:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn106" onclick="toggleCodeBlock('cloneGroup106', 'expandBtn106', 'collapseBtn106')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn106" onclick="toggleCodeBlock('cloneGroup106', 'expandBtn106', 'collapseBtn106')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup106"><code class="language-typescript text-sm text-gray-800">|| parts[3] !== this.options.jwtSecret) {
      return null;
    }
    
    try {
      const payload = JSON.parse(parts[1]) as TokenPayload;
      
      // Check if token is expired
      if (payload.exp &amp;&amp; payload.exp &lt; Math.floor(Date.now() / 1000)) {
        return null;
      }
      
      return payload;
    } catch (error) {
      return null;
    }
  }
  
  /**
   * Create a mock authentication middleware
   */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/services/segmentationQueueService.ts (Line 215:1 - Line 233:43), packages/backend/src/services/segmentationService.ts (Line 6:1 - Line 24:50)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn107" onclick="toggleCodeBlock('cloneGroup107', 'expandBtn107', 'collapseBtn107')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn107" onclick="toggleCodeBlock('cloneGroup107', 'expandBtn107', 'collapseBtn107')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup107"><code class="language-typescript text-sm text-gray-800">/**
 * Function to add a segmentation task to the queue
 *
 * @param imageId ID of the image
 * @param imagePath Path to the image
 * @param parameters Parameters for segmentation
 * @param priority Priority of the task
 * @returns Task ID
 */
export const triggerSegmentationTask = async (
  imageId: string,
  imagePath: string,
  parameters: any,
  priority: number = 1
): Promise&lt;string&gt; =&gt; {
  logger.info(`Queueing segmentation for imageId: ${imageId}, path: ${imagePath}, priority: ${priority}`);

  try {
    // Check if this is a force resegment task</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/services/projectDuplicationService.ts (Line 271:1 - Line 309:4), packages/backend/src/workers/projectDuplicationWorker.ts (Line 158:1 - Line 192:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn108" onclick="toggleCodeBlock('cloneGroup108', 'expandBtn108', 'collapseBtn108')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn108" onclick="toggleCodeBlock('cloneGroup108', 'expandBtn108', 'collapseBtn108')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup108"><code class="language-typescript text-sm text-gray-800">function generateNewFilePaths(
  originalStoragePath: string,
  originalThumbnailPath?: string,
  newProjectId?: string
): { newStoragePath: string; newThumbnailPath?: string } {
  // Generate timestamp and random suffix for uniqueness
  const timestamp = Date.now();
  const randomSuffix = Math.floor(Math.random() * 1000000);

  // Extract filename from original path
  const originalFileName = originalStoragePath.split('/').pop() || '';
  const fileNameParts = originalFileName.split('.');
  const fileExtension = fileNameParts.pop() || 'png';
  const fileBaseName = fileNameParts.join('.');

  // Generate new storage path
  const newStoragePath = `/uploads/${newProjectId}/${fileBaseName}-copy-${timestamp}-${randomSuffix}.${fileExtension}`;

  // Generate new thumbnail path if original exists
  let newThumbnailPath;
  if (originalThumbnailPath) {
    const originalThumbName = originalThumbnailPath.split('/').pop() || '';
    const thumbNameParts = originalThumbName.split('.');
    const thumbExtension = thumbNameParts.pop() || 'png';
    const thumbBaseName = thumbNameParts.join('.');

    newThumbnailPath = `/uploads/${newProjectId}/thumb-${thumbBaseName}-copy-${timestamp}-${randomSuffix}.${thumbExtension}`;
  }

  return { newStoragePath, newThumbnailPath };
}

/**
 * Copy image files from source to target
 *
 * @param sourcePath Source path (relative to baseDir)
 * @param targetPath Target path (relative to baseDir)
 * @param baseDir Base directory
 */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/services/projectDuplicationService.ts (Line 314:5 - Line 326:3), packages/backend/src/workers/projectDuplicationWorker.ts (Line 124:8 - Line 136:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn109" onclick="toggleCodeBlock('cloneGroup109', 'expandBtn109', 'collapseBtn109')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn109" onclick="toggleCodeBlock('cloneGroup109', 'expandBtn109', 'collapseBtn109')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup109"><code class="language-typescript text-sm text-gray-800">&gt; {
  try {
    // Normalize paths
    const normalizedSourcePath = sourcePath.startsWith('/') ? sourcePath.substring(1) : sourcePath;
    const normalizedTargetPath = targetPath.startsWith('/') ? targetPath.substring(1) : targetPath;

    // Create full paths
    const fullSourcePath = path.join(baseDir, normalizedSourcePath);
    const fullTargetPath = path.join(baseDir, normalizedTargetPath);

    // Create target directory if it doesn't exist
    const targetDir = path.dirname(fullTargetPath);
    if (!fs</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/services/metricsService.ts (Line 22:3 - Line 36:4), packages/frontend/src/pages/segmentation/hooks/polygonInteraction/geometry/utils/intersectionUtils.ts (Line 82:5 - Line 219:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn110" onclick="toggleCodeBlock('cloneGroup110', 'expandBtn110', 'collapseBtn110')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn110" onclick="toggleCodeBlock('cloneGroup110', 'expandBtn110', 'collapseBtn110')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup110"><code class="language-typescript text-sm text-gray-800">let area = 0;
  for (let i = 0; i &lt; points.length; i++) {
    const j = (i + 1) % points.length;
    area += points[i].x * points[j].y;
    area -= points[j].x * points[i].y;
  }
  
  return Math.abs(area / 2);
};

/**
 * Calculate the perimeter of a polygon
 * @param points Array of points defining the polygon
 * @returns Perimeter of the polygon
 */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/services/metricsService.ts (Line 59:3 - Line 83:4), packages/frontend/src/utils/polygonUtils.ts (Line 305:3 - Line 329:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn111" onclick="toggleCodeBlock('cloneGroup111', 'expandBtn111', 'collapseBtn111')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn111" onclick="toggleCodeBlock('cloneGroup111', 'expandBtn111', 'collapseBtn111')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup111"><code class="language-typescript text-sm text-gray-800">let minX = points[0].x;
  let minY = points[0].y;
  let maxX = points[0].x;
  let maxY = points[0].y;
  
  for (let i = 1; i &lt; points.length; i++) {
    minX = Math.min(minX, points[i].x);
    minY = Math.min(minY, points[i].y);
    maxX = Math.max(maxX, points[i].x);
    maxY = Math.max(maxY, points[i].y);
  }
  
  return {
    x: minX,
    y: minY,
    width: maxX - minX,
    height: maxY - minY
  };
};

/**
 * Calculate the convex hull of a polygon using Graham scan algorithm
 * @param points Array of points defining the polygon
 * @returns Array of points defining the convex hull
 */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/services/metricsService.ts (Line 84:1 - Line 105:5), packages/frontend/src/utils/polygonUtils.ts (Line 330:1 - Line 352:9)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn112" onclick="toggleCodeBlock('cloneGroup112', 'expandBtn112', 'collapseBtn112')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn112" onclick="toggleCodeBlock('cloneGroup112', 'expandBtn112', 'collapseBtn112')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup112"><code class="language-typescript text-sm text-gray-800">export const calculateConvexHull = (points: Point[]): Point[] =&gt; {
  if (points.length &lt;= 3) return [...points];
  
  // Find the point with the lowest y-coordinate (and leftmost if tied)
  let lowestPoint = points[0];
  for (let i = 1; i &lt; points.length; i++) {
    if (points[i].y &lt; lowestPoint.y || (points[i].y === lowestPoint.y &amp;&amp; points[i].x &lt; lowestPoint.x)) {
      lowestPoint = points[i];
    }
  }
  
  // Sort points by polar angle with respect to the lowest point
  const sortedPoints = [...points].sort((a, b) =&gt; {
    if (a === lowestPoint) return -1;
    if (b === lowestPoint) return 1;
    
    const angleA = Math.atan2(a.y - lowestPoint.y, a.x - lowestPoint.x);
    const angleB = Math.atan2(b.y - lowestPoint.y, b.x - lowestPoint.x);
    
    if (angleA === angleB) {
      // If angles are the same, sort by distance from the lowest point
      const distA = Math</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/services/metricsService.ts (Line 114:3 - Line 122:3), packages/frontend/src/utils/polygonUtils.ts (Line 361:3 - Line 371:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn113" onclick="toggleCodeBlock('cloneGroup113', 'expandBtn113', 'collapseBtn113')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn113" onclick="toggleCodeBlock('cloneGroup113', 'expandBtn113', 'collapseBtn113')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup113"><code class="language-typescript text-sm text-gray-800">const uniquePoints: Point[] = [];
  for (let i = 0; i &lt; sortedPoints.length; i++) {
    if (i === 0 || sortedPoints[i].x !== sortedPoints[i-1].x || sortedPoints[i].y !== sortedPoints[i-1].y) {
      uniquePoints.push(sortedPoints[i]);
    }
  }
  
  // Graham scan algorithm
  if (uniquePoints.length &lt;=</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/status.ts (Line 180:32 - Line 190:54), packages/backend/src/routes/status.ts (Line 78:27 - Line 88:25)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn114" onclick="toggleCodeBlock('cloneGroup114', 'expandBtn114', 'collapseBtn114')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn114" onclick="toggleCodeBlock('cloneGroup114', 'expandBtn114', 'collapseBtn114')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup114"><code class="language-typescript text-sm text-gray-800">, authMiddleware, async (req: AuthenticatedRequest, res: Response, next: NextFunction) =&gt; {
  const userId = req.user?.userId;
  const projectId = req.params.projectId;

  if (!userId) {
    res.status(401).json({ message: 'Authentication error' });
    return;
  }

  try {
    // Return mock data specific to the requested project</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/segmentation.ts (Line 119:14 - Line 144:39), packages/backend/src/routes/segmentation.ts (Line 52:8 - Line 77:38)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn115" onclick="toggleCodeBlock('cloneGroup115', 'expandBtn115', 'collapseBtn115')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn115" onclick="toggleCodeBlock('cloneGroup115', 'expandBtn115', 'collapseBtn115')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup115"><code class="language-typescript text-sm text-gray-800">,
                status: 'pending',
                result_data: {
                    polygons: []
                },
                polygons: [],
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };
            res.status(200).json(emptyResult);
            return;
        }

        // Format the result
        const segmentationResult = result.rows[0];

        // Ensure polygons are available in the expected format
        if (segmentationResult.result_data &amp;&amp; segmentationResult.result_data.polygons) {
            segmentationResult.polygons = segmentationResult.result_data.polygons;
        } else if (!segmentationResult.polygons) {
            segmentationResult.polygons = [];
        }

        res.status(200).json(segmentationResult);
    } catch (error) {
        console.error('Error fetching segmentation results:'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/segmentation.ts (Line 379:5 - Line 404:39), packages/backend/src/routes/segmentation.ts (Line 88:5 - Line 113:29)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn116" onclick="toggleCodeBlock('cloneGroup116', 'expandBtn116', 'collapseBtn116')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn116" onclick="toggleCodeBlock('cloneGroup116', 'expandBtn116', 'collapseBtn116')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup116"><code class="language-typescript text-sm text-gray-800">if (!userId) {
        res.status(401).json({ message: 'Authentication error' });
        return;
    }

    try {
        // Verify user has access to the project
        const projectCheck = await pool.query('SELECT id FROM projects WHERE id = $1 AND user_id = $2', [projectId, userId]);

        if (projectCheck.rows.length === 0) {
            res.status(404).json({ message: 'Project not found or access denied' });
            return;
        }

        // Verify image belongs to the project using its UUID
        const imageCheck = await pool.query('SELECT id FROM images WHERE id = $1 AND project_id = $2', [imageId, projectId]);

        if (imageCheck.rows.length === 0) {
            res.status(404).json({ message: 'Image not found in this project' });
            return;
        }

        // Use the actual image ID from the database for the segmentation lookup (which is just imageId now)
        const actualImageId = imageId; // Directly use the validated imageId

        // Check if segmentation result exists</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/segmentation.ts (Line 453:1 - Line 467:20), packages/backend/src/routes/status.ts (Line 146:1 - Line 159:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn117" onclick="toggleCodeBlock('cloneGroup117', 'expandBtn117', 'collapseBtn117')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn117" onclick="toggleCodeBlock('cloneGroup117', 'expandBtn117', 'collapseBtn117')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup117"><code class="language-typescript text-sm text-gray-800">router.get('/mock-queue-status', authMiddleware, async (req: AuthenticatedRequest, res: Response, next: NextFunction) =&gt; {
    const userId = req.user?.userId;

    if (!userId) {
        res.status(401).json({ message: 'Authentication error' });
        return;
    }

    try {
        // Return mock data to demonstrate the UI during development
        // Generate some random UUIDs for demonstration
        const mockImageId1 = '123e4567-e89b-12d3-a456-************';
        const mockImageId2 = '223e4567-e89b-12d3-a456-************';

        // Return mock data</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/projects.ts (Line 233:20 - Line 240:36), packages/backend/src/routes/projects.ts (Line 163:16 - Line 170:33)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn118" onclick="toggleCodeBlock('cloneGroup118', 'expandBtn118', 'collapseBtn118')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn118" onclick="toggleCodeBlock('cloneGroup118', 'expandBtn118', 'collapseBtn118')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup118"><code class="language-typescript text-sm text-gray-800">), async (req: AuthenticatedRequest, res: Response, next: NextFunction) =&gt; {
    const userId = req.user?.userId;
    const { id: projectId } = req.params;

    if (!userId) return res.status(401).json({ message: 'Authentication error' });

    try {
        logger.info('Processing delete project request'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/projects.ts (Line 242:9 - Line 264:40), packages/backend/src/routes/projects.ts (Line 188:9 - Line 210:33)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn119" onclick="toggleCodeBlock('cloneGroup119', 'expandBtn119', 'collapseBtn119')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn119" onclick="toggleCodeBlock('cloneGroup119', 'expandBtn119', 'collapseBtn119')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup119"><code class="language-typescript text-sm text-gray-800">// First check if the projects table exists
        const projectsTableCheck = await pool.query(`
            SELECT EXISTS (
                SELECT 1
                FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'projects'
            )
        `);
        
        const projectsTableExists = projectsTableCheck.rows[0].exists;
        if (!projectsTableExists) {
            logger.warn('Projects table does not exist in database');
            return res.status(404).json({ 
                message: 'Project not found - projects table missing',
                error: 'NOT_FOUND'
            });
        }

        // Import and use projectService
        const projectService = await import('../services/projectService');
        
        // Delete the project using the service</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/projects.ts (Line 295:2 - Line 316:60), packages/backend/src/routes/projects.ts (Line 240:2 - Line 207:33)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn120" onclick="toggleCodeBlock('cloneGroup120', 'expandBtn120', 'collapseBtn120')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn120" onclick="toggleCodeBlock('cloneGroup120', 'expandBtn120', 'collapseBtn120')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup120"><code class="language-typescript text-sm text-gray-800">});

        // First check if the projects table exists
        const projectsTableCheck = await pool.query(`
            SELECT EXISTS (
                SELECT 1
                FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'projects'
            )
        `);
        
        const projectsTableExists = projectsTableCheck.rows[0].exists;
        if (!projectsTableExists) {
            logger.warn('Projects table does not exist in database');
            return res.status(404).json({ 
                message: 'Project not found - projects table missing',
                error: 'NOT_FOUND'
            });
        }

        // Verify the source project exists and belongs to the user</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/projectDuplicationRoutes.ts (Line 89:7 - Line 100:39), packages/backend/src/routes/projects.ts (Line 316:9 - Line 327:79)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn121" onclick="toggleCodeBlock('cloneGroup121', 'expandBtn121', 'collapseBtn121')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn121" onclick="toggleCodeBlock('cloneGroup121', 'expandBtn121', 'collapseBtn121')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup121"><code class="language-typescript text-sm text-gray-800">// Verify the source project exists and belongs to the user
      const projectCheck = await pool.query(
        'SELECT * FROM projects WHERE id = $1 AND user_id = $2',
        [originalProjectId, userId]
      );

      if (projectCheck.rows.length === 0) {
        logger.info('Source project not found or access denied', { originalProjectId, userId });
        return res.status(404).json({ message: 'Source project not found or access denied' });
      }
      
      // Duplicate the project synchronously</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/projectDuplicationRoutes.ts (Line 120:11 - Line 134:40), packages/backend/src/routes/projects.ts (Line 313:2 - Line 327:79)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn122" onclick="toggleCodeBlock('cloneGroup122', 'expandBtn122', 'collapseBtn122')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn122" onclick="toggleCodeBlock('cloneGroup122', 'expandBtn122', 'collapseBtn122')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup122"><code class="language-typescript text-sm text-gray-800">);
    }

    // Verify the source project exists and belongs to the user
    const projectCheck = await pool.query(
      'SELECT * FROM projects WHERE id = $1 AND user_id = $2',
      [originalProjectId, userId]
    );

    if (projectCheck.rows.length === 0) {
      logger.info('Source project not found or access denied', { originalProjectId, userId });
      return res.status(404).json({ message: 'Source project not found or access denied' });
    }

    // Import the duplication queue service</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/projectDuplicationRoutes.ts (Line 134:5 - Line 159:4), packages/backend/src/routes/projects.ts (Line 342:21 - Line 367:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn123" onclick="toggleCodeBlock('cloneGroup123', 'expandBtn123', 'collapseBtn123')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn123" onclick="toggleCodeBlock('cloneGroup123', 'expandBtn123', 'collapseBtn123')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup123"><code class="language-typescript text-sm text-gray-800">// Import the duplication queue service
    const projectDuplicationQueueService = await import('../services/projectDuplicationQueueService');
    
    // Trigger an asynchronous duplication
    const taskId = await projectDuplicationQueueService.default.triggerProjectDuplication(
      pool,
      originalProjectId,
      userId,
      {
        newTitle,
        copyFiles,
        copySegmentations,
        resetStatus,
        baseDir: process.cwd()
      }
    );
    
    logger.info('Project duplication task created successfully', { 
      originalProjectId,
      taskId,
      userId,
      options: { newTitle, copyFiles, copySegmentations, resetStatus }
    });
    
    // Return the task ID and status
    res</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/projectDuplicationRoutes.ts (Line 279:24 - Line 286:30), packages/backend/src/routes/projectDuplicationRoutes.ts (Line 234:21 - Line 241:28)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn124" onclick="toggleCodeBlock('cloneGroup124', 'expandBtn124', 'collapseBtn124')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn124" onclick="toggleCodeBlock('cloneGroup124', 'expandBtn124', 'collapseBtn124')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup124"><code class="language-typescript text-sm text-gray-800">), async (req: AuthenticatedRequest, res: Response, next: NextFunction) =&gt; {
  const userId = req.user?.userId;
  const { taskId } = req.params;

  if (!userId) return res.status(401).json({ message: 'Authentication error' });

  try {
    logger.info('Cancelling duplication task'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/projectDuplicationRoutes.ts (Line 286:30 - Line 307:19), packages/backend/src/routes/projectDuplicationRoutes.ts (Line 241:28 - Line 262:16)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn125" onclick="toggleCodeBlock('cloneGroup125', 'expandBtn125', 'collapseBtn125')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn125" onclick="toggleCodeBlock('cloneGroup125', 'expandBtn125', 'collapseBtn125')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup125"><code class="language-typescript text-sm text-gray-800">, { userId, taskId });

    // Check if the duplication tasks table exists
    const tasksTableCheck = await pool.query(`
      SELECT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'project_duplication_tasks'
      )
    `);
    
    const tasksTableExists = tasksTableCheck.rows[0].exists;
    if (!tasksTableExists) {
      logger.warn('Project duplication tasks table does not exist');
      return res.status(404).json({ message: 'Task not found - tasks table missing' });
    }

    // Import the duplication queue service
    const projectDuplicationQueueService = await import('../services/projectDuplicationQueueService');
    
    // Cancel the task</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/images.ts (Line 604:18 - Line 619:32), packages/backend/src/routes/images.ts (Line 489:18 - Line 504:34)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn126" onclick="toggleCodeBlock('cloneGroup126', 'expandBtn126', 'collapseBtn126')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn126" onclick="toggleCodeBlock('cloneGroup126', 'expandBtn126', 'collapseBtn126')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup126"><code class="language-typescript text-sm text-gray-800">), async (req: AuthenticatedRequest, res: Response, next: NextFunction) =&gt; {
    const userId = req.user?.userId;
    let { projectId } = req.params;
    const imageId = req.params.imageId;

    // Handle project IDs with &quot;project-&quot; prefix
    const originalProjectId = projectId;
    if (projectId.startsWith('project-')) {
        projectId = projectId.substring(8); // Remove &quot;project-&quot; prefix for database query
        logger.debug('Removed project- prefix for database query', {
            originalId: originalProjectId,
            cleanedId: projectId
        });
    }

    logger.info('Image detail request received'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/images.ts (Line 691:5 - Line 702:5), packages/backend/src/routes/images.ts (Line 624:5 - Line 635:57)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn127" onclick="toggleCodeBlock('cloneGroup127', 'expandBtn127', 'collapseBtn127')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn127" onclick="toggleCodeBlock('cloneGroup127', 'expandBtn127', 'collapseBtn127')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup127"><code class="language-typescript text-sm text-gray-800">});

    try {
        const projectCheck = await pool.query('SELECT id FROM projects WHERE id = $1 AND user_id = $2', [projectId, userId]);

        if (projectCheck.rows.length === 0) {
            logger.warn('Project access denied', { projectId, originalProjectId, userId });
            throw new ApiError('Project not found or access denied', 404);
        }

        const imageResult = await pool.query(
            name</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/images.ts (Line 753:15 - Line 768:38), packages/backend/src/routes/images.ts (Line 489:2 - Line 504:34)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn128" onclick="toggleCodeBlock('cloneGroup128', 'expandBtn128', 'collapseBtn128')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn128" onclick="toggleCodeBlock('cloneGroup128', 'expandBtn128', 'collapseBtn128')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup128"><code class="language-typescript text-sm text-gray-800">, async (req: AuthenticatedRequest, res: Response, next: NextFunction) =&gt; {
    const userId = req.user?.userId;
    let { projectId } = req.params;
    const imageId = req.params.imageId;

    // Handle project IDs with &quot;project-&quot; prefix
    const originalProjectId = projectId;
    if (projectId.startsWith('project-')) {
        projectId = projectId.substring(8); // Remove &quot;project-&quot; prefix for database query
        logger.debug('Removed project- prefix for database query', {
            originalId: originalProjectId,
            cleanedId: projectId
        });
    }

    logger.info('Image verification request received'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/images.ts (Line 768:38 - Line 784:72), packages/backend/src/routes/images.ts (Line 619:32 - Line 635:57)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn129" onclick="toggleCodeBlock('cloneGroup129', 'expandBtn129', 'collapseBtn129')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn129" onclick="toggleCodeBlock('cloneGroup129', 'expandBtn129', 'collapseBtn129')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup129"><code class="language-typescript text-sm text-gray-800">, {
        userId,
        projectId,
        originalProjectId,
        imageId
    });

    try {
        const projectCheck = await pool.query('SELECT id FROM projects WHERE id = $1 AND user_id = $2', [projectId, userId]);

        if (projectCheck.rows.length === 0) {
            logger.warn('Project access denied', { projectId, originalProjectId, userId });
            throw new ApiError('Project not found or access denied', 404);
        }

        const imageResult = await pool.query(
            'SELECT id, storage_path FROM images WHERE id = $1 AND project_id = $2'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/images.ts (Line 856:2 - Line 868:3), packages/backend/src/routes/images.ts (Line 277:2 - Line 289:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn130" onclick="toggleCodeBlock('cloneGroup130', 'expandBtn130', 'collapseBtn130')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn130" onclick="toggleCodeBlock('cloneGroup130', 'expandBtn130', 'collapseBtn130')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup130"><code class="language-typescript text-sm text-gray-800">} = req.params;

    // Handle project IDs with &quot;project-&quot; prefix
    const originalProjectId = projectId;
    if (projectId.startsWith('project-')) {
        projectId = projectId.substring(8); // Remove &quot;project-&quot; prefix for database query
        logger.debug('Removed project- prefix for database query', {
            originalId: originalProjectId,
            cleanedId: projectId
        });
    }

    if</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/auth.ts (Line 386:6 - Line 394:29), packages/backend/src/routes/auth.ts (Line 365:10 - Line 373:39)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn132" onclick="toggleCodeBlock('cloneGroup132', 'expandBtn132', 'collapseBtn132')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn132" onclick="toggleCodeBlock('cloneGroup132', 'expandBtn132', 'collapseBtn132')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup132"><code class="language-typescript text-sm text-gray-800">, authMiddleware, async (req: AuthenticatedRequest, res: Response) =&gt; {
  const userId = req.user?.userId;

  if (!userId) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  try {
    logger.info('Fetching current user data'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/middleware/rateLimitMiddleware.ts (Line 92:42 - Line 99:7), packages/backend/src/middleware/rateLimitMiddleware.ts (Line 69:27 - Line 76:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn133" onclick="toggleCodeBlock('cloneGroup133', 'expandBtn133', 'collapseBtn133')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn133" onclick="toggleCodeBlock('cloneGroup133', 'expandBtn133', 'collapseBtn133')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup133"><code class="language-typescript text-sm text-gray-800">, { 
            ip: req.ip, 
            path: req.path,
            method: req.method,
            userId: (req as any).user?.userId 
          });
          res.status(429).json(options.message);
          res.set('Retry-After', '3600'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/middleware/rateLimitMiddleware.ts (Line 115:22 - Line 126:2), packages/backend/src/middleware/rateLimitMiddleware.ts (Line 69:27 - Line 80:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn134" onclick="toggleCodeBlock('cloneGroup134', 'expandBtn134', 'collapseBtn134')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn134" onclick="toggleCodeBlock('cloneGroup134', 'expandBtn134', 'collapseBtn134')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup134"><code class="language-typescript text-sm text-gray-800">, { 
            ip: req.ip, 
            path: req.path,
            method: req.method,
            userId: (req as any).user?.userId 
          });
          res.status(429).json(options.message);
          res.set('Retry-After', '900'); // 15 minutes in seconds
        }
      };
      break;
  }</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/middleware/rateLimitMiddleware.ts (Line 187:5 - Line 194:8), packages/backend/src/middleware/rateLimitMiddleware.ts (Line 91:6 - Line 98:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn135" onclick="toggleCodeBlock('cloneGroup135', 'expandBtn135', 'collapseBtn135')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn135" onclick="toggleCodeBlock('cloneGroup135', 'expandBtn135', 'collapseBtn135')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup135"><code class="language-typescript text-sm text-gray-800">: NextFunction, options: any) =&gt; {
    logger.warn('Sensitive operation rate limit exceeded', { 
      ip: req.ip, 
      path: req.path,
      method: req.method,
      userId: (req as any).user?.userId 
    });
    res.status(options</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/middleware/authMiddleware.ts (Line 161:5 - Line 184:37), packages/backend/src/middleware/authMiddleware.ts (Line 51:5 - Line 74:22)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn136" onclick="toggleCodeBlock('cloneGroup136', 'expandBtn136', 'collapseBtn136')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn136" onclick="toggleCodeBlock('cloneGroup136', 'expandBtn136', 'collapseBtn136')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup136"><code class="language-typescript text-sm text-gray-800">const decoded = tokenService.verifyToken(token, TokenType.ACCESS, {
      validateFingerprint: config.auth.tokenSecurityMode === 'strict',
      requiredIssuer: 'spheroseg-auth',
      requiredAudience: 'spheroseg-api'
    });

    // Add user data to request
    req.user = {
      userId: decoded.userId,
      email: decoded.email,
      type: decoded.type,
      // Add additional claims for enhanced security
      tokenId: decoded.jti,
      fingerprint: decoded.fingerprint,
      tokenVersion: decoded.version
    };

    // Add token metadata to request for access in routes
    req.tokenMetadata = {
      issuedAt: new Date((decoded as any).iat * 1000),
      expiresAt: new Date((decoded as any).exp * 1000)
    };

    logger.debug(`Optional auth: User authenticated: </code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/controllers/segmentationController.ts (Line 191:2 - Line 213:7), packages/backend/src/controllers/segmentationController.ts (Line 158:2 - Line 180:12)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn137" onclick="toggleCodeBlock('cloneGroup137', 'expandBtn137', 'collapseBtn137')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn137" onclick="toggleCodeBlock('cloneGroup137', 'expandBtn137', 'collapseBtn137')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup137"><code class="language-typescript text-sm text-gray-800">= async (
  req: Request,
  res: Response,
  next: NextFunction
) =&gt; {
  try {
    const imageId = req.params.imageId;
    const versionStr = req.params.version;
    
    if (!imageId) {
      throw new ApiError(400, 'Image ID is required');
    }
    
    if (!versionStr) {
      throw new ApiError(400, 'Version is required');
    }
    
    const version = parseInt(versionStr, 10);
    if (isNaN(version) || version &lt;= 0) {
      throw new ApiError(400, 'Invalid version number');
    }
    
    const result</code></pre></div><div class="py-4"><p class="text-gray-600">packages/types/src/user.ts (Line 14:1 - Line 24:16), packages/frontend/src/types/userProfile.ts (Line 1:1 - Line 11:94)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn138" onclick="toggleCodeBlock('cloneGroup138', 'expandBtn138', 'collapseBtn138')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn138" onclick="toggleCodeBlock('cloneGroup138', 'expandBtn138', 'collapseBtn138')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup138"><code class="language-typescript text-sm text-gray-800">export interface UserProfile {
  user_id: string;
  username: string | null;
  full_name: string | null;
  title: string | null;
  organization: string | null;
  bio: string | null;
  location: string | null;
  avatar_url: string | null;
  preferred_language: string | null;
  preferred_theme</code></pre></div><div class="py-4"><p class="text-gray-600">packages/types/src/user.d.ts (Line 1:1 - Line 46:2), packages/types/src/user.ts (Line 1:1 - Line 53:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn139" onclick="toggleCodeBlock('cloneGroup139', 'expandBtn139', 'collapseBtn139')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn139" onclick="toggleCodeBlock('cloneGroup139', 'expandBtn139', 'collapseBtn139')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup139"><code class="language-typescript text-sm text-gray-800">/**
 * User and authentication related types
 */
export interface User {
    id: string;
    email: string;
    name?: string;
    role?: 'user' | 'admin';
    created_at?: string;
    updated_at?: string;
}
export interface UserProfile {
    user_id: string;
    username: string | null;
    full_name: string | null;
    title: string | null;
    organization: string | null;
    bio: string | null;
    location: string | null;
    avatar_url: string | null;
    preferred_language: string | null;
    preferred_theme?: 'light' | 'dark' | 'system' | null;
    storage_limit_bytes?: number | null;
    storage_used_bytes?: number | null;
}
export interface LoginResponse {
    token: string;
    user: User;
}
export interface RefreshTokenResponse {
    token: string;
    expires_at: string;
}
export type UserProfileUpdatePayload = Partial&lt;Omit&lt;UserProfile, 'user_id'&gt;&gt;;
export interface AccessRequestPayload {
    email: string;
    name?: string;
    organization?: string;
    reason?: string;
}
export interface AccessRequestResponse {
    id: string;
    email: string;
    status: string;
    created_at: string;
}</code></pre></div><div class="py-4"><p class="text-gray-600">packages/types/src/segmentation.d.ts (Line 12:1 - Line 59:2), packages/types/src/segmentation.ts (Line 15:1 - Line 67:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn140" onclick="toggleCodeBlock('cloneGroup140', 'expandBtn140', 'collapseBtn140')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn140" onclick="toggleCodeBlock('cloneGroup140', 'expandBtn140', 'collapseBtn140')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup140"><code class="language-typescript text-sm text-gray-800">}
export interface SegmentationData {
    id: string;
    image_id?: string;
    imageId?: string;
    status?: ImageStatus;
    result_data?: {
        polygons: Polygon[];
        [key: string]: any;
    };
    polygons: Polygon[];
    created_at?: string;
    updated_at?: string;
    parameters?: {
        model?: string;
        threshold?: number;
        [key: string]: any;
    };
    [key: string]: any;
}
export interface SegmentationResult {
    id: string;
    polygons: Polygon[];
    [key: string]: any;
}
export interface SegmentationResultData {
    polygons?: Polygon[];
    contours?: Array&lt;Array&lt;[number, number]&gt;&gt;;
    hierarchy?: Array&lt;[number, number, number, number]&gt;;
    imageWidth: number;
    imageHeight: number;
    metadata?: {
        source?: 'resunet' | 'api' | 'empty' | 'cv2';
        timestamp?: string;
        modelType?: string;
        [key: string]: unknown;
    };
}
export interface SegmentationApiResponse {
    image_id: string;
    status: ImageStatus;
    result_data?: SegmentationResultData | null;
    parameters?: Record&lt;string, unknown&gt; | null;
    created_at: string;
    updated_at: string;
    error?: string | null;
}
export type CanvasSegmentationData = SegmentationResultData;</code></pre></div><div class="py-4"><p class="text-gray-600">packages/types/src/project.d.ts (Line 1:1 - Line 30:2), packages/types/src/project.ts (Line 1:1 - Line 33:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn141" onclick="toggleCodeBlock('cloneGroup141', 'expandBtn141', 'collapseBtn141')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn141" onclick="toggleCodeBlock('cloneGroup141', 'expandBtn141', 'collapseBtn141')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup141"><code class="language-typescript text-sm text-gray-800">/**
 * Project related types
 */
export interface Project {
    id: string;
    user_id: string;
    title: string;
    description: string | null;
    created_at: string;
    updated_at: string;
    thumbnail_url?: string | null;
    image_count?: number;
}
export type ProjectCreatePayload = Pick&lt;Project, 'title' | 'description'&gt;;
export interface ProjectStatsResponse {
    user_id: string;
    project_count: number;
    image_count: number;
    segmentation_count: number;
    recently_updated_projects: Array&lt;{
        id: string;
        title: string;
        updated_at: string;
    }&gt;;
    storage_usage: {
        total_bytes: number;
        images_bytes: number;
        segmentations_bytes: number;
    };
}</code></pre></div><div class="py-4"><p class="text-gray-600">packages/types/src/polygon.d.ts (Line 1:1 - Line 44:8), packages/types/src/polygon.ts (Line 1:1 - Line 52:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn142" onclick="toggleCodeBlock('cloneGroup142', 'expandBtn142', 'collapseBtn142')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn142" onclick="toggleCodeBlock('cloneGroup142', 'expandBtn142', 'collapseBtn142')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup142"><code class="language-typescript text-sm text-gray-800">/**
 * Types related to polygons and geometric objects
 */
export interface Point {
    x: number;
    y: number;
}
export interface Polygon {
    id: string;
    points: Point[];
    type?: 'external' | 'internal';
    class?: string;
    color?: string;
    parentId?: string;
    [key: string]: any;
}
export interface VertexHoverInfo {
    polygonId: string | null;
    vertexIndex: number | null;
}
export interface VertexDragInfo {
    polygonId: string | null;
    vertexIndex: number | null;
    isDragging: boolean;
}
export interface DragInfo {
    isDragging: boolean;
    startX: number;
    startY: number;
    lastX: number;
    lastY: number;
}
export interface TempPointsInfo {
    points: Point[];
    polygonId?: string | null;
    startIndex?: number | null;
    endIndex?: number | null;
}
export interface TransformState {
    zoom: number;
    translateX: number;
    translateY: number;
}
export declare</code></pre></div><div class="py-4"><p class="text-gray-600">packages/types/src/image.d.ts (Line 1:1 - Line 51:2), packages/types/src/image.ts (Line 1:1 - Line 55:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn143" onclick="toggleCodeBlock('cloneGroup143', 'expandBtn143', 'collapseBtn143')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn143" onclick="toggleCodeBlock('cloneGroup143', 'expandBtn143', 'collapseBtn143')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup143"><code class="language-typescript text-sm text-gray-800">/**
 * Image related types
 */
import { ImageStatus } from './segmentation';
export interface ImageData {
    id: string;
    name: string;
    width: number;
    height: number;
    src: string;
    storage_path?: string;
    storage_path_full?: string;
    project_id?: string;
    projectId?: string;
    user_id?: string;
    created_at?: string;
    updated_at?: string;
    status?: ImageStatus;
    actualId?: string;
    [key: string]: any;
}
export interface Image {
    id: string;
    project_id: string;
    user_id: string;
    name: string;
    storage_path: string;
    thumbnail_path: string | null;
    width: number | null;
    height: number | null;
    metadata: Record&lt;string, unknown&gt; | null;
    status: ImageStatus;
    created_at: string;
    updated_at: string;
    segmentation_result?: {
        path?: string | null;
    } | null;
}
export interface ProjectImage {
    id: string;
    project_id: string;
    name: string;
    url: string;
    thumbnail_url: string | null;
    createdAt: Date;
    updatedAt: Date;
    width: number | null;
    height: number | null;
    segmentationStatus: ImageStatus;
    segmentationResultPath?: string | null;
}</code></pre></div><!-- Add more clone groups for .txt format as needed         // Add more clone groups for .txt format as needed--></div><a name="tsx-clones"></a><h2 class="text-2xl font-semibold text-gray-700 mb-4">tsx</h2><div class="divide-y divide-gray-200 border-b-2"><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/components/canvas/CanvasV2.tsx (Line 93:5 - Line 106:7), packages/frontend/src/pages/segmentation/hooks/polygonInteraction/editMode/useTempPoints.tsx (Line 50:3 - Line 63:9)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn3" onclick="toggleCodeBlock('cloneGroup3', 'expandBtn3', 'collapseBtn3')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn3" onclick="toggleCodeBlock('cloneGroup3', 'expandBtn3', 'collapseBtn3')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup3"><code class="language-tsx text-sm text-gray-800">useEffect(() =&gt; {
        const handleKeyDown = (e: KeyboardEvent) =&gt; {
            if (e.key === 'Shift') {
                setIsShiftPressed(true);
            }
        };

        const handleKeyUp = (e: KeyboardEvent) =&gt; {
            if (e.key === 'Shift') {
                setIsShiftPressed(false);
            }
        };

        window</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/components/canvas/CanvasV2.tsx (Line 760:17 - Line 772:64), packages/frontend/src/pages/segmentation/components/canvas/CanvasV2.tsx (Line 666:13 - Line 678:42)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn4" onclick="toggleCodeBlock('cloneGroup4', 'expandBtn4', 'collapseBtn4')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn4" onclick="toggleCodeBlock('cloneGroup4', 'expandBtn4', 'collapseBtn4')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup4"><code class="language-tsx text-sm text-gray-800">${index}`}
                                    cx={point.x}
                                    cy={point.y}
                                    r={index === 0 ? vertexRadius * 1.5 : vertexRadius} // Make first point larger
                                    fill={index === 0 ? &quot;yellow&quot; : &quot;cyan&quot;} // Make first point a different color
                                    stroke=&quot;black&quot;
                                    strokeWidth={1 / transform.zoom}
                                    vectorEffect=&quot;non-scaling-stroke&quot;
                                    style={{ pointerEvents: 'none' }}
                                /&gt;
                            ))}

                            {/* Draw line from start vertex to first temp point or cursor */</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/components/canvas/CanvasPolygon.tsx (Line 32:3 - Line 39:16), packages/frontend/src/pages/segmentation/components/canvas/PolygonCollection.tsx (Line 19:3 - Line 26:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn5" onclick="toggleCodeBlock('cloneGroup5', 'expandBtn5', 'collapseBtn5')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn5" onclick="toggleCodeBlock('cloneGroup5', 'expandBtn5', 'collapseBtn5')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup5"><code class="language-tsx text-sm text-gray-800">editMode: EditMode;
  onSelectPolygon?: (id: string) =&gt; void;
  onDeletePolygon?: (id: string) =&gt; void;
  onSlicePolygon?: (id: string) =&gt; void;
  onEditPolygon?: (id: string) =&gt; void;
  onDeleteVertex?: (polygonId: string, vertexIndex: number) =&gt; void;
  onDuplicateVertex?: (polygonId: string, vertexIndex: number) =&gt; void;
  relatedPolygons</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/__tests__/keyboard/SegmentationEditorV2KeyboardTests.tsx (Line 17:3 - Line 39:2), packages/frontend/src/pages/segmentation/__tests__/polygon/SegmentationEditorV2PolygonTests.tsx (Line 16:3 - Line 38:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn6" onclick="toggleCodeBlock('cloneGroup6', 'expandBtn6', 'collapseBtn6')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn6" onclick="toggleCodeBlock('cloneGroup6', 'expandBtn6', 'collapseBtn6')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup6"><code class="language-tsx text-sm text-gray-800">const mockState = {
    imageData: {
      id: 'test-image-id',
      actualId: 'test-image-id',
      name: 'test-image.jpg',
      url: 'https://example.com/test-image.jpg',
      width: 800,
      height: 600,
    },
    segmentationData: {
      polygons: [
        {
          id: 'polygon-1',
          points: [
            { x: 100, y: 100 },
            { x: 200, y: 100 },
            { x: 200, y: 200 },
            { x: 100, y: 200 },
          ],
          color: '#FF0000',
          label: 'Cell 1',
        },
      ]</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/__tests__/keyboard/SegmentationEditorV2KeyboardTests.tsx (Line 59:31 - Line 66:2), packages/frontend/src/pages/segmentation/__tests__/polygon/SegmentationEditorV2PolygonTests.tsx (Line 79:17 - Line 86:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn7" onclick="toggleCodeBlock('cloneGroup7', 'expandBtn7', 'collapseBtn7')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn7" onclick="toggleCodeBlock('cloneGroup7', 'expandBtn7', 'collapseBtn7')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup7"><code class="language-tsx text-sm text-gray-800">: vi.fn(),
    handleSave: vi.fn(),
    undo: vi.fn(),
    redo: vi.fn(),
    onMouseDown: vi.fn(),
    onMouseMove: vi.fn(),
    onMouseUp: vi.fn(),
    getCanvasCoordinates: vi.fn()</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/__tests__/keyboard/SegmentationEditorV2KeyboardTests.tsx (Line 66:2 - Line 85:24), packages/frontend/src/pages/segmentation/__tests__/polygon/SegmentationEditorV2PolygonTests.tsx (Line 86:2 - Line 105:48)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn8" onclick="toggleCodeBlock('cloneGroup8', 'expandBtn8', 'collapseBtn8')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn8" onclick="toggleCodeBlock('cloneGroup8', 'expandBtn8', 'collapseBtn8')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup8"><code class="language-tsx text-sm text-gray-800">),
    handleDeletePolygon: vi.fn(),
  };

  return {
    useSegmentationV2: vi.fn(() =&gt; mockState),
    EditMode: {
      View: 'View',
      EditVertices: 'EditVertices',
      AddPolygon: 'AddPolygon',
      DeletePolygon: 'DeletePolygon',
      SlicePolygon: 'SlicePolygon',
      MergePolygons: 'MergePolygons',
    },
    // Export the mock state so we can modify it during tests
    _mockSegmentationState: mockState
  };
});

// Mock useSlicing hook</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/__tests__/keyboard/SegmentationEditorV2KeyboardTests.tsx (Line 90:2 - Line 101:27), packages/frontend/src/pages/segmentation/__tests__/polygon/SegmentationEditorV2PolygonTests.tsx (Line 146:2 - Line 157:63)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn9" onclick="toggleCodeBlock('cloneGroup9', 'expandBtn9', 'collapseBtn9')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn9" onclick="toggleCodeBlock('cloneGroup9', 'expandBtn9', 'collapseBtn9')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup9"><code class="language-tsx text-sm text-gray-800">);

// Mock react-router-dom's useNavigate
vi.mock('react-router-dom', async () =&gt; {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: vi.fn(() =&gt; vi.fn()),
  };
});

// Mock CanvasV2 component</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/__tests__/keyboard/SegmentationEditorV2KeyboardTests.tsx (Line 119:5 - Line 141:45), packages/frontend/src/pages/segmentation/__tests__/polygon/SegmentationEditorV2PolygonTests.tsx (Line 216:5 - Line 238:44)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn10" onclick="toggleCodeBlock('cloneGroup10', 'expandBtn10', 'collapseBtn10')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn10" onclick="toggleCodeBlock('cloneGroup10', 'expandBtn10', 'collapseBtn10')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup10"><code class="language-tsx text-sm text-gray-800">&lt;/div&gt;
  )),
}));

// Mock toast
vi.mock('sonner', () =&gt; ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

// Mock react-i18next
vi.mock('react-i18next', () =&gt; ({
  useTranslation: () =&gt; ({
    t: (key: string) =&gt; key,
  }),
}));

// Mock fetch for API calls
global.fetch = vi.fn();

describe('SegmentationEditorV2 Keyboard Interactions'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/__tests__/keyboard/SegmentationEditorV2KeyboardTests.tsx (Line 141:45 - Line 165:3), packages/frontend/src/pages/segmentation/__tests__/polygon/SegmentationEditorV2PolygonTests.tsx (Line 238:44 - Line 262:28)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn11" onclick="toggleCodeBlock('cloneGroup11', 'expandBtn11', 'collapseBtn11')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn11" onclick="toggleCodeBlock('cloneGroup11', 'expandBtn11', 'collapseBtn11')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup11"><code class="language-tsx text-sm text-gray-800">, () =&gt; {
  beforeEach(() =&gt; {
    // Setup all context mocks
    setupAllContextMocks();
    
    // Reset mocks
    vi.clearAllMocks();
  });

  afterEach(() =&gt; {
    vi.clearAllMocks();
  });

  const renderComponent = () =&gt; {
    return render(
      &lt;MemoryRouterWrapper&gt;
        &lt;SegmentationEditorV2 
          projectId=&quot;test-project-id&quot; 
          imageId=&quot;test-image-id&quot; 
        /&gt;
      &lt;/MemoryRouterWrapper&gt;
    );
  };

  it</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/components/EditorToolbar.tsx (Line 104:2 - Line 117:18), packages/frontend/src/pages/segmentation/components/EditorToolbar.tsx (Line 85:2 - Line 98:16)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn13" onclick="toggleCodeBlock('cloneGroup13', 'expandBtn13', 'collapseBtn13')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn13" onclick="toggleCodeBlock('cloneGroup13', 'expandBtn13', 'collapseBtn13')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup13"><code class="language-tsx text-sm text-gray-800">)&lt;/span&gt;
          &lt;/TooltipContent&gt;
        &lt;/Tooltip&gt;
      &lt;/TooltipProvider&gt;

      &lt;TooltipProvider delayDuration={300}&gt;
        &lt;Tooltip&gt;
          &lt;TooltipTrigger asChild&gt;
            &lt;Button
              variant=&quot;ghost&quot;
              size=&quot;icon&quot;
              className=&quot;h-9 w-9 text-foreground/80 hover:bg-muted hover:text-foreground&quot;
              onClick={onResetView}
              data-testid=&quot;reset-view-button</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/ui/enhanced-file-uploader.tsx (Line 116:2 - Line 136:3), packages/frontend/src/components/ui/file-uploader.tsx (Line 66:8 - Line 86:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn31" onclick="toggleCodeBlock('cloneGroup31', 'expandBtn31', 'collapseBtn31')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn31" onclick="toggleCodeBlock('cloneGroup31', 'expandBtn31', 'collapseBtn31')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup31"><code class="language-tsx text-sm text-gray-800">]
  );

  // Convert accept string to the format expected by react-dropzone v14+
  const acceptProp = typeof accept === 'string' 
    ? { [accept]: [] }  // Convert 'image/*' to { 'image/*': [] }
    : accept;

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: acceptProp,
    multiple,
    maxSize,
    disabled: disabled || uploadState === 'uploading',
    onDrop: handleDrop,
    onDragEnter: () =&gt; setDragActive(true),
    onDragLeave: () =&gt; setDragActive(false),
  });

  // Handle file removal
  const removeFile = (index: number) =&gt; {
    if</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/ui/enhanced-file-uploader.tsx (Line 237:2 - Line 246:2), packages/frontend/src/components/ui/file-uploader.tsx (Line 170:2 - Line 179:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn32" onclick="toggleCodeBlock('cloneGroup32', 'expandBtn32', 'collapseBtn32')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn32" onclick="toggleCodeBlock('cloneGroup32', 'expandBtn32', 'collapseBtn32')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup32"><code class="language-tsx text-sm text-gray-800">(
        &lt;div className=&quot;flex-1 min-w-0&quot;&gt;
          &lt;p className=&quot;text-sm font-medium text-gray-900 dark:text-gray-100 truncate&quot;&gt;
            {file.name}
          &lt;/p&gt;
          &lt;p className=&quot;text-xs text-gray-500 dark:text-gray-400&quot;&gt;
            {(file.size / 1024).toFixed(2)} kB
          &lt;/p&gt;
        &lt;/div&gt;
      );</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/ui/enhanced-file-uploader.tsx (Line 331:13 - Line 344:3), packages/frontend/src/components/ui/file-uploader.tsx (Line 123:10 - Line 136:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn33" onclick="toggleCodeBlock('cloneGroup33', 'expandBtn33', 'collapseBtn33')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn33" onclick="toggleCodeBlock('cloneGroup33', 'expandBtn33', 'collapseBtn33')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup33"><code class="language-tsx text-sm text-gray-800">}&lt;/span&gt;
        &lt;/div&gt;
      );
    }

    return null;
  };

  // File preview section
  const renderPreview = () =&gt; {
    if (!showPreview || files.length === 0) return null;

    return (
      &lt;div className={cn</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/ui/enhanced-file-uploader.tsx (Line 411:2 - Line 425:16), packages/frontend/src/components/ui/file-uploader.tsx (Line 219:12 - Line 233:11)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn34" onclick="toggleCodeBlock('cloneGroup34', 'expandBtn34', 'collapseBtn34')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn34" onclick="toggleCodeBlock('cloneGroup34', 'expandBtn34', 'collapseBtn34')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup34"><code class="language-tsx text-sm text-gray-800">;
  };

  return (
    &lt;div className={cn(&quot;space-y-4&quot;, className)}&gt;
      &lt;div
        {...getRootProps()}
        className={getContainerClasses()}
      &gt;
        &lt;input {...getInputProps()} /&gt;
        
        &lt;div className=&quot;p-8 text-center&quot;&gt;
          &lt;UploadCloud className=&quot;mx-auto h-12 w-12 text-gray-400&quot; /&gt;
          &lt;p className=&quot;mt-2 text-sm font-medium text-gray-700 dark:text-gray-300&quot;&gt;
            {getDropzoneText</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/SegmentationProgress.tsx (Line 170:17 - Line 182:2), packages/frontend/src/components/project/SegmentationProgress.tsx (Line 83:15 - Line 94:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn38" onclick="toggleCodeBlock('cloneGroup38', 'expandBtn38', 'collapseBtn38')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn38" onclick="toggleCodeBlock('cloneGroup38', 'expandBtn38', 'collapseBtn38')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup38"><code class="language-tsx text-sm text-gray-800">console.log(`Using mock data for project ${projectId}...`);
                setQueueStatus({
                  queueLength: 1,
                  runningTasks: ['123e4567-e89b-12d3-a456-************'],
                  queuedTasks: ['323e4567-e89b-12d3-a456-426614174002'],
                  processingImages: [
                    { id: '123e4567-e89b-12d3-a456-************', name: `Sample Image for Project ${projectId}`, projectId }
                  ]
                });
                return; // Exit early with mock data
              }
            }
          }</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/SegmentationProgress.tsx (Line 201:13 - Line 214:2), packages/frontend/src/components/project/SegmentationProgress.tsx (Line 112:11 - Line 124:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn39" onclick="toggleCodeBlock('cloneGroup39', 'expandBtn39', 'collapseBtn39')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn39" onclick="toggleCodeBlock('cloneGroup39', 'expandBtn39', 'collapseBtn39')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup39"><code class="language-tsx text-sm text-gray-800">console.log('Using mock data for global queue...');
            setQueueStatus({
              queueLength: 2,
              runningTasks: ['123e4567-e89b-12d3-a456-************', '223e4567-e89b-12d3-a456-************'],
              queuedTasks: ['323e4567-e89b-12d3-a456-426614174002', '423e4567-e89b-12d3-a456-426614174003'],
              processingImages: [
                { id: '123e4567-e89b-12d3-a456-************', name: 'Sample Image 1', projectId: 'project-123' },
                { id: '223e4567-e89b-12d3-a456-************', name: 'Sample Image 2', projectId: 'project-456' }
              ]
            });
            return; // Exit early with mock data
          }
        }
      }</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/ProjectImageProcessor.tsx (Line 84:25 - Line 91:17), packages/frontend/src/components/project/ProjectImageProcessor.tsx (Line 46:32 - Line 53:15)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn41" onclick="toggleCodeBlock('cloneGroup41', 'expandBtn41', 'collapseBtn41')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn41" onclick="toggleCodeBlock('cloneGroup41', 'expandBtn41', 'collapseBtn41')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup41"><code class="language-tsx text-sm text-gray-800">;
       if (axios.isAxiosError(error) &amp;&amp; error.response) {
          message = error.response.data?.message || message;
       } else if (error instanceof Error) {
         message = error.message;
       }
      toast.error(message);
      setLoadingStatus</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/ImageCard.tsx (Line 82:15 - Line 102:15), packages/frontend/src/components/project/ImageListItem.tsx (Line 63:13 - Line 83:13)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn43" onclick="toggleCodeBlock('cloneGroup43', 'expandBtn43', 'collapseBtn43')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn43" onclick="toggleCodeBlock('cloneGroup43', 'expandBtn43', 'collapseBtn43')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup43"><code class="language-tsx text-sm text-gray-800">onError={(e) =&gt; {
                console.error(`Failed to load thumbnail: ${image.thumbnail_url}`);
                try {
                  // Try with direct URL to backend including port
                  const backendUrl = import.meta.env.VITE_API_URL || 'http://backend:5001';
                  const directPath = `${backendUrl}/uploads/${image.thumbnail_url?.replace(/^.*uploads\//, '')}`;
                  console.log(`Trying direct backend URL: ${directPath}`);
                  e.currentTarget.src = directPath;
                } catch (err) {
                  // Fallback to original image if thumbnail fails
                  if (image.url) {
                    e.currentTarget.src = constructUrl(image.url);
                  } else {
                    e.currentTarget.src = '/placeholder.svg';
                  }
                }
              }}
            /&gt;
          ) : image.url ? (
            &lt;img
              </code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/ImageActions.tsx (Line 15:5 - Line 43:13), packages/frontend/src/components/project/ImageListActions.tsx (Line 15:8 - Line 43:17)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn44" onclick="toggleCodeBlock('cloneGroup44', 'expandBtn44', 'collapseBtn44')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn44" onclick="toggleCodeBlock('cloneGroup44', 'expandBtn44', 'collapseBtn44')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup44"><code class="language-tsx text-sm text-gray-800">&quot;
          size=&quot;icon&quot;
          className=&quot;h-7 w-7&quot;
          onClick={(e) =&gt; {
            e.stopPropagation();
            onResegment();
          }}
          title=&quot;Opětovná segmentace&quot;
        &gt;
          &lt;RefreshCcw className=&quot;h-4 w-4&quot; /&gt;
        &lt;/Button&gt;
      )}
      &lt;Button
        variant=&quot;destructive&quot;
        size=&quot;icon&quot;
        className=&quot;h-7 w-7&quot;
        onClick={(e) =&gt; {
          e.stopPropagation();
          onDelete();
        }}
        title=&quot;Smazat obrázek&quot;
      &gt;
        &lt;Trash2 className=&quot;h-4 w-4&quot; /&gt;
      &lt;/Button&gt;
    &lt;/div&gt;
  );
};

export default ImageActions</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/__tests__/ImageUploaderTest.tsx (Line 109:22 - Line 120:22), packages/frontend/src/components/__tests__/ImageUploaderTest.tsx (Line 89:25 - Line 100:66)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn45" onclick="toggleCodeBlock('cloneGroup45', 'expandBtn45', 'collapseBtn45')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn45" onclick="toggleCodeBlock('cloneGroup45', 'expandBtn45', 'collapseBtn45')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup45"><code class="language-tsx text-sm text-gray-800">, async () =&gt; {
    renderComponent();
    
    // Wait for projects to load
    await waitFor(() =&gt; {
      expect(apiClient.get).toHaveBeenCalledWith('/projects?limit=1000');
    });
    
    // Create a mock file
    const file = new File(['test'], 'test-image.jpg', { type: 'image/jpeg' });
    
    // Simulate file drop</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/SignIn.tsx (Line 81:7 - Line 98:4), packages/frontend/src/pages/SignUp.tsx (Line 117:5 - Line 134:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn63" onclick="toggleCodeBlock('cloneGroup63', 'expandBtn63', 'collapseBtn63')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn63" onclick="toggleCodeBlock('cloneGroup63', 'expandBtn63', 'collapseBtn63')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup63"><code class="language-tsx text-sm text-gray-800">&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    );
  }

  return (
    &lt;div className=&quot;relative min-h-screen flex flex-col items-center justify-center p-4 bg-gradient-to-br from-blue-100 via-purple-100 to-pink-100 dark:from-gray-800 dark:via-gray-900 dark:to-black&quot;&gt;
      {/* Header Buttons - Positioned Absolutely */}
      &lt;div className=&quot;absolute top-4 left-4&quot;&gt;
        &lt;BackButton /&gt;
      &lt;/div&gt;
      &lt;div className=&quot;absolute top-4 right-4&quot;&gt;
        &lt;LanguageSwitcher /&gt;
      &lt;/div&gt;

      {/* Main Content Card - Centered */}
      &lt;div</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/SignIn.tsx (Line 174:14 - Line 185:39), packages/frontend/src/pages/SignUp.tsx (Line 295:14 - Line 306:58)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn64" onclick="toggleCodeBlock('cloneGroup64', 'expandBtn64', 'collapseBtn64')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn64" onclick="toggleCodeBlock('cloneGroup64', 'expandBtn64', 'collapseBtn64')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup64"><code class="language-tsx text-sm text-gray-800">)}
              &lt;/Button&gt;
            &lt;/Link&gt;
            &lt;Link to=&quot;/request-access&quot;&gt;
              &lt;Button
                variant=&quot;outline&quot;
                className=&quot;w-full h-10 text-base rounded-md&quot;
              &gt;
                {t('auth.requestAccess')}
              &lt;/Button&gt;
            &lt;/Link&gt;
            &lt;p className=&quot;text-center text-sm text-gray-600 mt-3</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/SignIn.tsx (Line 185:39 - Line 197:4), packages/frontend/src/pages/SignUp.tsx (Line 306:58 - Line 318:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn65" onclick="toggleCodeBlock('cloneGroup65', 'expandBtn65', 'collapseBtn65')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn65" onclick="toggleCodeBlock('cloneGroup65', 'expandBtn65', 'collapseBtn65')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup65"><code class="language-tsx text-sm text-gray-800">&quot;&gt;
              {t('auth.termsAndPrivacy')}{' '}
              &lt;Link to=&quot;/terms-of-service&quot; className=&quot;font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors&quot;&gt;
                {t('common.termsOfService')}
              &lt;/Link&gt;{' '}
              {t('requestAccess.and')}{' '}
              &lt;Link to=&quot;/privacy-policy&quot; className=&quot;font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors&quot;&gt;
                {t('common.privacyPolicy')}
              &lt;/Link&gt;
            &lt;/p&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/RequestAccess.tsx (Line 87:3 - Line 99:2), packages/frontend/src/pages/SignUp.tsx (Line 123:3 - Line 135:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn66" onclick="toggleCodeBlock('cloneGroup66', 'expandBtn66', 'collapseBtn66')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn66" onclick="toggleCodeBlock('cloneGroup66', 'expandBtn66', 'collapseBtn66')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup66"><code class="language-tsx text-sm text-gray-800">return (
    &lt;div className=&quot;relative min-h-screen flex flex-col items-center justify-center p-4 bg-gradient-to-br from-blue-100 via-purple-100 to-pink-100 dark:from-gray-800 dark:via-gray-900 dark:to-black&quot;&gt;
      {/* Header Buttons - Positioned Absolutely */}
      &lt;div className=&quot;absolute top-4 left-4&quot;&gt;
        &lt;BackButton /&gt;
      &lt;/div&gt;
      &lt;div className=&quot;absolute top-4 right-4&quot;&gt;
        &lt;LanguageSwitcher /&gt;
      &lt;/div&gt;

      {/* Main Content Card - Centered */}
      &lt;Card className=&quot;w-full max-w-md shadow-xl bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-gray-200 dark:border-gray-700/50 rounded-lg overflow-hidden&quot;&gt;
        {</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/RequestAccess.tsx (Line 210:9 - Line 218:25), packages/frontend/src/pages/SignUp.tsx (Line 278:9 - Line 285:26)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn67" onclick="toggleCodeBlock('cloneGroup67', 'expandBtn67', 'collapseBtn67')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn67" onclick="toggleCodeBlock('cloneGroup67', 'expandBtn67', 'collapseBtn67')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup67"><code class="language-tsx text-sm text-gray-800">&lt;/CardContent&gt;

        &lt;div className=&quot;p-6 bg-gray-50 dark:bg-gray-800/50 border-t border-gray-100 dark:border-gray-700/50&quot;&gt;
          &lt;div className=&quot;relative&quot;&gt;
            &lt;div className=&quot;absolute inset-0 flex items-center&quot;&gt;
              &lt;div className=&quot;w-full border-t border-gray-300 dark:border-gray-700&quot;&gt;&lt;/div&gt;
            &lt;/div&gt;
            &lt;div className=&quot;relative flex justify-center text-sm&quot;&gt;
              &lt;span className=&quot;px-4 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400&quot;&gt;{t('auth.alreadyHaveAccess'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/RequestAccess.tsx (Line 218:25 - Line 231:9), packages/frontend/src/pages/SignUp.tsx (Line 285:26 - Line 298:16)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn68" onclick="toggleCodeBlock('cloneGroup68', 'expandBtn68', 'collapseBtn68')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn68" onclick="toggleCodeBlock('cloneGroup68', 'expandBtn68', 'collapseBtn68')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup68"><code class="language-tsx text-sm text-gray-800">)}&lt;/span&gt;
            &lt;/div&gt;
          &lt;/div&gt;

          &lt;div className=&quot;mt-6 flex flex-col gap-3&quot;&gt;
            &lt;Link to=&quot;/sign-in&quot;&gt;
              &lt;Button
                variant=&quot;outline&quot;
                className=&quot;w-full h-10 text-base rounded-md&quot;
              &gt;
                {t('auth.signIn')}
              &lt;/Button&gt;
            &lt;/Link&gt;
            &lt;Link to=&quot;/sign-up</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/RequestAccess.tsx (Line 236:14 - Line 256:14), packages/frontend/src/pages/SignUp.tsx (Line 303:21 - Line 323:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn69" onclick="toggleCodeBlock('cloneGroup69', 'expandBtn69', 'collapseBtn69')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn69" onclick="toggleCodeBlock('cloneGroup69', 'expandBtn69', 'collapseBtn69')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup69"><code class="language-tsx text-sm text-gray-800">)}
              &lt;/Button&gt;
            &lt;/Link&gt;
            &lt;p className=&quot;text-center text-sm text-gray-600 dark:text-gray-400 mt-3&quot;&gt;
              {t('auth.termsAndPrivacy')}{' '}
              &lt;Link to=&quot;/terms-of-service&quot; className=&quot;font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors&quot;&gt;
                {t('common.termsOfService')}
              &lt;/Link&gt;{' '}
              {t('requestAccess.and')}{' '}
              &lt;Link to=&quot;/privacy-policy&quot; className=&quot;font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors&quot;&gt;
                {t('common.privacyPolicy')}
              &lt;/Link&gt;
            &lt;/p&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/Card&gt;
    &lt;/div&gt;
  );
};

export default RequestAccess</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/ProjectDetail.tsx (Line 302:2 - Line 312:3), packages/frontend/src/pages/ProjectDetail.tsx (Line 271:2 - Line 281:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn70" onclick="toggleCodeBlock('cloneGroup70', 'expandBtn70', 'collapseBtn70')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn70" onclick="toggleCodeBlock('cloneGroup70', 'expandBtn70', 'collapseBtn70')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup70"><code class="language-tsx text-sm text-gray-800">= () =&gt; {
    const selectedImageIds = Object.entries(selectedImages)
      .filter(([_, isSelected]) =&gt; isSelected)
      .map(([id]) =&gt; id);

    if (selectedImageIds.length === 0) {
      toast.error('No images selected');
      return;
    }

    if</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/ProjectDetail.tsx (Line 363:2 - Line 373:67), packages/frontend/src/pages/ProjectDetail.tsx (Line 271:2 - Line 281:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn71" onclick="toggleCodeBlock('cloneGroup71', 'expandBtn71', 'collapseBtn71')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn71" onclick="toggleCodeBlock('cloneGroup71', 'expandBtn71', 'collapseBtn71')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup71"><code class="language-tsx text-sm text-gray-800">= () =&gt; {
    const selectedImageIds = Object.entries(selectedImages)
      .filter(([_, isSelected]) =&gt; isSelected)
      .map(([id]) =&gt; id);

    if (selectedImageIds.length === 0) {
      toast.error('No images selected');
      return;
    }

    // Použijeme funkci z useExportFunctions pro vytvoření ZIP souboru</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/ProjectDetail.tsx (Line 417:40 - Line 424:2), packages/frontend/src/components/project/ProjectImageProcessor.tsx (Line 46:32 - Line 53:15)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn72" onclick="toggleCodeBlock('cloneGroup72', 'expandBtn72', 'collapseBtn72')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn72" onclick="toggleCodeBlock('cloneGroup72', 'expandBtn72', 'collapseBtn72')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup72"><code class="language-tsx text-sm text-gray-800">;
         if (axios.isAxiosError(error) &amp;&amp; error.response) {
            message = error.response.data?.message || message;
         } else if (error instanceof Error) {
           message = error.message;
         }
        toast.error(message);
      }</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/Profile.tsx (Line 19:1 - Line 31:10), packages/frontend/src/pages/Settings.tsx (Line 22:1 - Line 34:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn74" onclick="toggleCodeBlock('cloneGroup74', 'expandBtn74', 'collapseBtn74')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn74" onclick="toggleCodeBlock('cloneGroup74', 'expandBtn74', 'collapseBtn74')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup74"><code class="language-tsx text-sm text-gray-800">interface UserProfile {
  user_id: string;
  username: string | null;
  full_name: string | null;
  title: string | null;
  organization: string | null;
  bio: string | null;
  location: string | null;
  avatar_url: string | null;
  preferred_language: string | null;
}

interface</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/Profile.tsx (Line 349:2 - Line 356:8), packages/frontend/src/pages/ProjectDetail.tsx (Line 449:5 - Line 457:14)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn75" onclick="toggleCodeBlock('cloneGroup75', 'expandBtn75', 'collapseBtn75')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn75" onclick="toggleCodeBlock('cloneGroup75', 'expandBtn75', 'collapseBtn75')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup75"><code class="language-tsx text-sm text-gray-800">&gt;
        {/* Background elements */}
        &lt;div className=&quot;absolute inset-0 -z-10 pointer-events-none&quot;&gt;
          &lt;div className=&quot;absolute top-1/3 left-1/4 w-64 h-64 bg-blue-200/30 dark:bg-blue-400/10 rounded-full filter blur-3xl animate-float&quot; /&gt;
          &lt;div className=&quot;absolute bottom-1/4 right-1/3 w-80 h-80 bg-blue-300/20 dark:bg-blue-500/10 rounded-full filter blur-3xl animate-float&quot; style={{ animationDelay: &quot;-2s&quot; }} /&gt;
          &lt;div className=&quot;absolute top-2/3 left-1/3 w-40 h-40 bg-blue-400/20 dark:bg-blue-600/10 rounded-full filter blur-3xl animate-float&quot; style={{ animationDelay: &quot;-4s&quot; }} /&gt;
        &lt;/div&gt;
        &lt;Loader2</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/Profile.tsx (Line 361:5 - Line 370:4), packages/frontend/src/pages/Profile.tsx (Line 347:8 - Line 457:14)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn76" onclick="toggleCodeBlock('cloneGroup76', 'expandBtn76', 'collapseBtn76')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn76" onclick="toggleCodeBlock('cloneGroup76', 'expandBtn76', 'collapseBtn76')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup76"><code class="language-tsx text-sm text-gray-800">) {
    return (
      &lt;div className=&quot;min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 relative flex justify-center items-center&quot;&gt;
        {/* Background elements */}
        &lt;div className=&quot;absolute inset-0 -z-10 pointer-events-none&quot;&gt;
          &lt;div className=&quot;absolute top-1/3 left-1/4 w-64 h-64 bg-blue-200/30 dark:bg-blue-400/10 rounded-full filter blur-3xl animate-float&quot; /&gt;
          &lt;div className=&quot;absolute bottom-1/4 right-1/3 w-80 h-80 bg-blue-300/20 dark:bg-blue-500/10 rounded-full filter blur-3xl animate-float&quot; style={{ animationDelay: &quot;-2s&quot; }} /&gt;
          &lt;div className=&quot;absolute top-2/3 left-1/3 w-40 h-40 bg-blue-400/20 dark:bg-blue-600/10 rounded-full filter blur-3xl animate-float&quot; style={{ animationDelay: &quot;-4s&quot; }} /&gt;
        &lt;/div&gt;
        &lt;div</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/Profile.tsx (Line 377:12 - Line 386:82), packages/frontend/src/pages/Profile.tsx (Line 347:8 - Line 370:51)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn77" onclick="toggleCodeBlock('cloneGroup77', 'expandBtn77', 'collapseBtn77')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn77" onclick="toggleCodeBlock('cloneGroup77', 'expandBtn77', 'collapseBtn77')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup77"><code class="language-tsx text-sm text-gray-800">) {
    return (
      &lt;div className=&quot;min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 relative flex justify-center items-center&quot;&gt;
        {/* Background elements */}
        &lt;div className=&quot;absolute inset-0 -z-10 pointer-events-none&quot;&gt;
          &lt;div className=&quot;absolute top-1/3 left-1/4 w-64 h-64 bg-blue-200/30 dark:bg-blue-400/10 rounded-full filter blur-3xl animate-float&quot; /&gt;
          &lt;div className=&quot;absolute bottom-1/4 right-1/3 w-80 h-80 bg-blue-300/20 dark:bg-blue-500/10 rounded-full filter blur-3xl animate-float&quot; style={{ animationDelay: &quot;-2s&quot; }} /&gt;
          &lt;div className=&quot;absolute top-2/3 left-1/3 w-40 h-40 bg-blue-400/20 dark:bg-blue-600/10 rounded-full filter blur-3xl animate-float&quot; style={{ animationDelay: &quot;-4s&quot; }} /&gt;
        &lt;/div&gt;
        &lt;div className=&quot;p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md text-red-500 dark:text-red-400</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/Profile.tsx (Line 399:114 - Line 407:16), packages/frontend/src/pages/Profile.tsx (Line 349:133 - Line 457:14)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn78" onclick="toggleCodeBlock('cloneGroup78', 'expandBtn78', 'collapseBtn78')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn78" onclick="toggleCodeBlock('cloneGroup78', 'expandBtn78', 'collapseBtn78')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup78"><code class="language-tsx text-sm text-gray-800">&quot;&gt;
      {/* Background elements */}
      &lt;div className=&quot;absolute inset-0 -z-10 pointer-events-none&quot;&gt;
        &lt;div className=&quot;absolute top-1/3 left-1/4 w-64 h-64 bg-blue-200/30 dark:bg-blue-400/10 rounded-full filter blur-3xl animate-float&quot; /&gt;
        &lt;div className=&quot;absolute bottom-1/4 right-1/3 w-80 h-80 bg-blue-300/20 dark:bg-blue-500/10 rounded-full filter blur-3xl animate-float&quot; style={{ animationDelay: &quot;-2s&quot; }} /&gt;
        &lt;div className=&quot;absolute top-2/3 left-1/3 w-40 h-40 bg-blue-400/20 dark:bg-blue-600/10 rounded-full filter blur-3xl animate-float&quot; style={{ animationDelay: &quot;-4s&quot; }} /&gt;
      &lt;/div&gt;

      &lt;DashboardHeader</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/PrivacyPolicy.tsx (Line 7:2 - Line 15:20), packages/frontend/src/pages/TermsOfService.tsx (Line 7:2 - Line 15:18)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn79" onclick="toggleCodeBlock('cloneGroup79', 'expandBtn79', 'collapseBtn79')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn79" onclick="toggleCodeBlock('cloneGroup79', 'expandBtn79', 'collapseBtn79')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup79"><code class="language-tsx text-sm text-gray-800">= () =&gt; {
  const { t } = useLanguage();

  return (
    &lt;div className=&quot;min-h-screen flex flex-col bg-gradient-to-br from-blue-100 via-purple-100 to-pink-100 dark:from-gray-800 dark:via-gray-900 dark:to-black&quot;&gt;
      &lt;Navbar /&gt;
      &lt;div className=&quot;container mx-auto px-4 py-12 flex-1 mt-16&quot;&gt;
        &lt;div className=&quot;max-w-4xl mx-auto&quot;&gt;
          &lt;h1 className=&quot;text-3xl font-bold mb-8&quot;&gt;{t('privacyPage.title'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/PrivacyPolicy.tsx (Line 73:2 - Line 81:18), packages/frontend/src/pages/TermsOfService.tsx (Line 48:5 - Line 56:16)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn80" onclick="toggleCodeBlock('cloneGroup80', 'expandBtn80', 'collapseBtn80')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn80" onclick="toggleCodeBlock('cloneGroup80', 'expandBtn80', 'collapseBtn80')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup80"><code class="language-tsx text-sm text-gray-800">&lt;/p&gt;
          &lt;/div&gt;

          &lt;div className=&quot;mt-8 flex justify-between&quot;&gt;
            &lt;Button variant=&quot;outline&quot; asChild&gt;
              &lt;Link to=&quot;/&quot;&gt;{t('common.backToHome')}&lt;/Link&gt;
            &lt;/Button&gt;
            &lt;Button asChild&gt;
              &lt;Link to=&quot;/terms-of-service</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/Dashboard.tsx (Line 96:116 - Line 105:4), packages/frontend/src/pages/Profile.tsx (Line 349:133 - Line 408:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn81" onclick="toggleCodeBlock('cloneGroup81', 'expandBtn81', 'collapseBtn81')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn81" onclick="toggleCodeBlock('cloneGroup81', 'expandBtn81', 'collapseBtn81')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup81"><code class="language-tsx text-sm text-gray-800">&quot;&gt;
        {/* Background elements */}
        &lt;div className=&quot;absolute inset-0 -z-10 pointer-events-none&quot;&gt;
          &lt;div className=&quot;absolute top-1/3 left-1/4 w-64 h-64 bg-blue-200/30 dark:bg-blue-400/10 rounded-full filter blur-3xl animate-float&quot; /&gt;
          &lt;div className=&quot;absolute bottom-1/4 right-1/3 w-80 h-80 bg-blue-300/20 dark:bg-blue-500/10 rounded-full filter blur-3xl animate-float&quot; style={{ animationDelay: &quot;-2s&quot; }} /&gt;
          &lt;div className=&quot;absolute top-2/3 left-1/3 w-40 h-40 bg-blue-400/20 dark:bg-blue-600/10 rounded-full filter blur-3xl animate-float&quot; style={{ animationDelay: &quot;-4s&quot; }} /&gt;
        &lt;/div&gt;

        &lt;DashboardHeader /&gt;
        &lt;div</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/Dashboard.tsx (Line 117:3 - Line 129:75), packages/frontend/src/pages/Dashboard.tsx (Line 95:5 - Line 106:105)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn82" onclick="toggleCodeBlock('cloneGroup82', 'expandBtn82', 'collapseBtn82')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn82" onclick="toggleCodeBlock('cloneGroup82', 'expandBtn82', 'collapseBtn82')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup82"><code class="language-tsx text-sm text-gray-800">return (
    &lt;div className=&quot;min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 relative overflow-hidden&quot;&gt;
      {/* Background elements */}
      &lt;div className=&quot;absolute inset-0 -z-10 pointer-events-none&quot;&gt;
        &lt;div className=&quot;absolute top-1/3 left-1/4 w-64 h-64 bg-blue-200/30 dark:bg-blue-400/10 rounded-full filter blur-3xl animate-float&quot; /&gt;
        &lt;div className=&quot;absolute bottom-1/4 right-1/3 w-80 h-80 bg-blue-300/20 dark:bg-blue-500/10 rounded-full filter blur-3xl animate-float&quot; style={{ animationDelay: &quot;-2s&quot; }} /&gt;
        &lt;div className=&quot;absolute top-2/3 left-1/3 w-40 h-40 bg-blue-400/20 dark:bg-blue-600/10 rounded-full filter blur-3xl animate-float&quot; style={{ animationDelay: &quot;-4s&quot; }} /&gt;
      &lt;/div&gt;

      &lt;DashboardHeader /&gt;

      &lt;div className=&quot;container mx-auto px-4 py-8 relative z-10&quot;&gt;
        &lt;div className=&quot;flex flex-col md:flex-row items-start md:items-center justify-between mb-8</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/context/SocketContext.tsx (Line 1:1 - Line 73:2), packages/frontend/src/contexts/SocketContext.tsx (Line 1:1 - Line 73:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn90" onclick="toggleCodeBlock('cloneGroup90', 'expandBtn90', 'collapseBtn90')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn90" onclick="toggleCodeBlock('cloneGroup90', 'expandBtn90', 'collapseBtn90')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup90"><code class="language-tsx text-sm text-gray-800">/**
 * SocketContext
 * 
 * Provides global Socket.IO connection management for the application
 */
import React, { createContext, useContext, useMemo, ReactNode } from 'react';
import { Socket } from 'socket.io-client';
import useSocketConnection from '../hooks/useSocketConnection';

interface SocketContextType {
  socket: Socket | null;
  isConnected: boolean;
  error: Error | null;
  connect: () =&gt; void;
  disconnect: () =&gt; void;
}

// Create context with default values
const SocketContext = createContext&lt;SocketContextType&gt;({
  socket: null,
  isConnected: false,
  error: null,
  connect: () =&gt; {},
  disconnect: () =&gt; {},
});

interface SocketProviderProps {
  children: ReactNode;
  autoConnect?: boolean;
}

/**
 * Socket context provider component
 */
export const SocketProvider: React.FC&lt;SocketProviderProps&gt; = ({ 
  children, 
  autoConnect = true 
}) =&gt; {
  const { socket, isConnected, error, connect, disconnect } = useSocketConnection({
    autoConnect,
    reconnect: true,
  });

  // Memoize context value to prevent unnecessary re-renders
  const value = useMemo(() =&gt; ({
    socket,
    isConnected,
    error,
    connect,
    disconnect,
  }), [socket, isConnected, error, connect, disconnect]);

  return (
    &lt;SocketContext.Provider value={value}&gt;
      {children}
    &lt;/SocketContext.Provider&gt;
  );
};

/**
 * Custom hook to use the socket context
 */
export const useSocket = (): SocketContextType =&gt; {
  const context = useContext(SocketContext);
  
  if (context === undefined) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  
  return context;
};

export default SocketContext;</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/StatsOverview.tsx (Line 94:29 - Line 101:9), packages/frontend/src/components/project/ProjectImageProcessor.tsx (Line 46:32 - Line 53:15)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn93" onclick="toggleCodeBlock('cloneGroup93', 'expandBtn93', 'collapseBtn93')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn93" onclick="toggleCodeBlock('cloneGroup93', 'expandBtn93', 'collapseBtn93')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup93"><code class="language-tsx text-sm text-gray-800">;
        if (axios.isAxiosError(error) &amp;&amp; error.response) {
          message = error.response.data?.message || message;
        } else if (error instanceof Error) {
          message = error.message;
        }
        toast.error(message);
        setStats</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/NewProject.tsx (Line 86:9 - Line 99:17), packages/frontend/src/components/project/ProjectDialogForm.tsx (Line 32:7 - Line 45:15)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn95" onclick="toggleCodeBlock('cloneGroup95', 'expandBtn95', 'collapseBtn95')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn95" onclick="toggleCodeBlock('cloneGroup95', 'expandBtn95', 'collapseBtn95')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup95"><code class="language-tsx text-sm text-gray-800">&lt;DialogHeader&gt;
          &lt;DialogTitle&gt;{t('projects.createProject')}&lt;/DialogTitle&gt;
          &lt;DialogDescription&gt;
            {t('projects.createProjectDesc')}
          &lt;/DialogDescription&gt;
        &lt;/DialogHeader&gt;
        &lt;form onSubmit={handleCreateProject}&gt;
          &lt;div className=&quot;grid gap-4 py-4&quot;&gt;
            &lt;div className=&quot;space-y-2&quot;&gt;
              &lt;Label htmlFor=&quot;projectName&quot; className=&quot;text-right&quot;&gt;
                {t('common.projectName')}
              &lt;/Label&gt;
              &lt;Input
                </code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/Features.tsx (Line 71:5 - Line 94:2), packages/frontend/src/components/ThemedFooter.tsx (Line 49:5 - Line 72:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn97" onclick="toggleCodeBlock('cloneGroup97', 'expandBtn97', 'collapseBtn97')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn97" onclick="toggleCodeBlock('cloneGroup97', 'expandBtn97', 'collapseBtn97')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup97"><code class="language-tsx text-sm text-gray-800">// Navigate through the translation object to find the value
    let value = translations;
    for (const part of parts) {
      if (value &amp;&amp; typeof value === 'object' &amp;&amp; part in value) {
        value = value[part];
      } else {
        // If the key doesn't exist in the current language, fall back to English
        if (language !== 'en') {
          let englishValue = enTranslations;
          for (const p of parts) {
            if (englishValue &amp;&amp; typeof englishValue === 'object' &amp;&amp; p in englishValue) {
              englishValue = englishValue[p];
            } else {
              return key; // Key not found in English either
            }
          }
          return typeof englishValue === 'string' ? englishValue : key;
        }
        return key; // Key not found
      }
    }
    
    return typeof value === 'string' ? value : key;
  };</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/Features.tsx (Line 94:2 - Line 108:3), packages/frontend/src/components/Hero.tsx (Line 11:2 - Line 25:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn98" onclick="toggleCodeBlock('cloneGroup98', 'expandBtn98', 'collapseBtn98')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn98" onclick="toggleCodeBlock('cloneGroup98', 'expandBtn98', 'collapseBtn98')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup98"><code class="language-tsx text-sm text-gray-800">;
  
  useEffect(() =&gt; {
    const observer = new IntersectionObserver(
      (entries) =&gt; {
        entries.forEach((entry) =&gt; {
          if (entry.isIntersecting) {
            entry.target.classList.add(&quot;active&quot;);
          }
        });
      },
      { threshold: 0.1 }
    );

    if</code></pre></div><!-- Add more clone groups for .txt format as needed         // Add more clone groups for .txt format as needed--></div><a name="javascript-clones"></a><h2 class="text-2xl font-semibold text-gray-700 mb-4">javascript</h2><div class="divide-y divide-gray-200 border-b-2"><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/components/canvas/CanvasV2.tsx (Line 303:10 - Line 323:10), packages/frontend/src/pages/segmentation/components/canvas/CanvasV2.tsx (Line 295:10 - Line 309:10)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn0" onclick="toggleCodeBlock('cloneGroup0', 'expandBtn0', 'collapseBtn0')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn0" onclick="toggleCodeBlock('cloneGroup0', 'expandBtn0', 'collapseBtn0')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup0"><code class="language-javascript text-sm text-gray-800">, marginBottom: '4px' }}&gt;Create Polygon Mode&lt;/div&gt;
                            &lt;div&gt;1. Click to start creating a polygon&lt;/div&gt;
                            &lt;div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.8 }}&gt;Hold SHIFT to automatically add points&lt;/div&gt;
                        &lt;/div&gt;
                    ) : tempPoints.length &lt; 3 ? (
                        &lt;div&gt;
                            &lt;div style={{ color: '#4ade80', marginBottom: '4px' }}&gt;Create Polygon Mode&lt;/div&gt;
                            &lt;div&gt;2. Continue clicking to add more points (at least 3 needed)&lt;/div&gt;
                            &lt;div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.8 }}&gt;Hold SHIFT to automatically add points • Press ESC to cancel&lt;/div&gt;
                        &lt;/div&gt;
                    ) : (
                        &lt;div&gt;
                            &lt;div style={{ color: '#4ade80', marginBottom: '4px' }}&gt;Create Polygon Mode&lt;/div&gt;
                            &lt;div&gt;3. Continue adding points or click near the first point to close the polygon&lt;/div&gt;
                            &lt;div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.8 }}&gt;Hold SHIFT to automatically add points • Press ESC to cancel&lt;/div&gt;
                        &lt;/div&gt;
                    )
                ) : editMode === EditMode.AddPoints ? (
                    !interactionState?.isAddingPoints ? (
                        &lt;div&gt;
                            &lt;div style={{ color: '#60a5fa'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/components/canvas/CanvasV2.tsx (Line 323:10 - Line 337:10), packages/frontend/src/pages/segmentation/components/canvas/CanvasV2.tsx (Line 315:10 - Line 329:10)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn1" onclick="toggleCodeBlock('cloneGroup1', 'expandBtn1', 'collapseBtn1')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn1" onclick="toggleCodeBlock('cloneGroup1', 'expandBtn1', 'collapseBtn1')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup1"><code class="language-javascript text-sm text-gray-800">, marginBottom: '4px' }}&gt;Add Points Mode&lt;/div&gt;
                            &lt;div&gt;Click on any vertex to start adding points&lt;/div&gt;
                            &lt;div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.8 }}&gt;Press ESC to cancel&lt;/div&gt;
                        &lt;/div&gt;
                    ) : (
                        &lt;div&gt;
                            &lt;div style={{ color: '#60a5fa', marginBottom: '4px' }}&gt;Add Points Mode&lt;/div&gt;
                            &lt;div&gt;Click to add points, then click on another vertex to complete&lt;/div&gt;
                            &lt;div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.8 }}&gt;Hold SHIFT to automatically add points • Press ESC to cancel&lt;/div&gt;
                        &lt;/div&gt;
                    )
                ) : editMode === EditMode.EditVertices ? (
                    selectedPolygonId ? (
                        &lt;div&gt;
                            &lt;div style={{ color: '#f97316'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/components/canvas/CanvasV2.tsx (Line 337:10 - Line 349:10), packages/frontend/src/pages/segmentation/components/canvas/CanvasV2.tsx (Line 329:10 - Line 343:10)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn2" onclick="toggleCodeBlock('cloneGroup2', 'expandBtn2', 'collapseBtn2')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn2" onclick="toggleCodeBlock('cloneGroup2', 'expandBtn2', 'collapseBtn2')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup2"><code class="language-javascript text-sm text-gray-800">, marginBottom: '4px' }}&gt;Edit Vertices Mode&lt;/div&gt;
                            &lt;div&gt;Click and drag vertices to move them&lt;/div&gt;
                            &lt;div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.8 }}&gt;Hold SHIFT and click a vertex to add points • Double-click a vertex to delete it&lt;/div&gt;
                        &lt;/div&gt;
                    ) : (
                        &lt;div&gt;
                            &lt;div style={{ color: '#f97316', marginBottom: '4px' }}&gt;Edit Vertices Mode&lt;/div&gt;
                            &lt;div style={{ fontSize: '12px', marginTop: '4px', opacity: 0.8 }}&gt;Click on a polygon to select it for editing&lt;/div&gt;
                        &lt;/div&gt;
                    )
                ) : editMode === EditMode.DeletePolygon ? (
                    &lt;div&gt;
                        &lt;div style={{ color: '#ef4444'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/segmentation/SegmentationEditorV2.tsx (Line 196:9 - Line 213:10), packages/frontend/src/pages/segmentation/SegmentationPage.tsx (Line 207:9 - Line 225:12)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn28" onclick="toggleCodeBlock('cloneGroup28', 'expandBtn28', 'collapseBtn28')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn28" onclick="toggleCodeBlock('cloneGroup28', 'expandBtn28', 'collapseBtn28')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup28"><code class="language-javascript text-sm text-gray-800">={editMode}
        setEditMode={setEditMode}
        onZoomIn={handleZoomIn}
        onZoomOut={handleZoomOut}
        onResetView={handleResetView}
        onSave={handleSave}
        onUndo={undo}
        onRedo={redo}
        onResegment={handleResegment}
        canUndo={canUndo}
        canRedo={canRedo}
        isSaving={isSaving}
        isResegmenting={isResegmenting}
      /&gt;

      &lt;div className=&quot;flex-1 relative&quot; id=&quot;canvas-container&quot;&gt;
        &lt;CanvasV2
          imageData={imageData</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/ui/context-menu.tsx (Line 65:7 - Line 145:52), packages/frontend/src/components/ui/menubar.tsx (Line 100:9 - Line 180:36)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn35" onclick="toggleCodeBlock('cloneGroup35', 'expandBtn35', 'collapseBtn35')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn35" onclick="toggleCodeBlock('cloneGroup35', 'expandBtn35', 'collapseBtn35')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup35"><code class="language-javascript text-sm text-gray-800">)}
      {...props}
    /&gt;
  &lt;/ContextMenuPrimitive.Portal&gt;
))
ContextMenuContent.displayName = ContextMenuPrimitive.Content.displayName

const ContextMenuItem = React.forwardRef&lt;
  React.ElementRef&lt;typeof ContextMenuPrimitive.Item&gt;,
  React.ComponentPropsWithoutRef&lt;typeof ContextMenuPrimitive.Item&gt; &amp; {
    inset?: boolean
  }
&gt;(({ className, inset, ...props }, ref) =&gt; (
  &lt;ContextMenuPrimitive.Item
    ref={ref}
    className={cn(
      &quot;relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50&quot;,
      inset &amp;&amp; &quot;pl-8&quot;,
      className
    )}
    {...props}
  /&gt;
))
ContextMenuItem.displayName = ContextMenuPrimitive.Item.displayName

const ContextMenuCheckboxItem = React.forwardRef&lt;
  React.ElementRef&lt;typeof ContextMenuPrimitive.CheckboxItem&gt;,
  React.ComponentPropsWithoutRef&lt;typeof ContextMenuPrimitive.CheckboxItem&gt;
&gt;(({ className, children, checked, ...props }, ref) =&gt; (
  &lt;ContextMenuPrimitive.CheckboxItem
    ref={ref}
    className={cn(
      &quot;relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50&quot;,
      className
    )}
    checked={checked}
    {...props}
  &gt;
    &lt;span className=&quot;absolute left-2 flex h-3.5 w-3.5 items-center justify-center&quot;&gt;
      &lt;ContextMenuPrimitive.ItemIndicator&gt;
        &lt;Check className=&quot;h-4 w-4&quot; /&gt;
      &lt;/ContextMenuPrimitive.ItemIndicator&gt;
    &lt;/span&gt;
    {children}
  &lt;/ContextMenuPrimitive.CheckboxItem&gt;
))
ContextMenuCheckboxItem.displayName =
  ContextMenuPrimitive.CheckboxItem.displayName

const ContextMenuRadioItem = React.forwardRef&lt;
  React.ElementRef&lt;typeof ContextMenuPrimitive.RadioItem&gt;,
  React.ComponentPropsWithoutRef&lt;typeof ContextMenuPrimitive.RadioItem&gt;
&gt;(({ className, children, ...props }, ref) =&gt; (
  &lt;ContextMenuPrimitive.RadioItem
    ref={ref}
    className={cn(
      &quot;relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50&quot;,
      className
    )}
    {...props}
  &gt;
    &lt;span className=&quot;absolute left-2 flex h-3.5 w-3.5 items-center justify-center&quot;&gt;
      &lt;ContextMenuPrimitive.ItemIndicator&gt;
        &lt;Circle className=&quot;h-2 w-2 fill-current&quot; /&gt;
      &lt;/ContextMenuPrimitive.ItemIndicator&gt;
    &lt;/span&gt;
    {children}
  &lt;/ContextMenuPrimitive.RadioItem&gt;
))
ContextMenuRadioItem.displayName = ContextMenuPrimitive.RadioItem.displayName

const ContextMenuLabel = React.forwardRef&lt;
  React.ElementRef&lt;typeof ContextMenuPrimitive.Label&gt;,
  React.ComponentPropsWithoutRef&lt;typeof ContextMenuPrimitive.Label&gt; &amp; {
    inset?: boolean
  }
&gt;(({ className, inset, ...props }, ref) =&gt; (
  &lt;ContextMenuPrimitive.Label
    ref={ref}
    className={cn(
      &quot;px-2 py-1.5 text-sm font-semibold text-foreground&quot;</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/settings/UserProfileSection.tsx (Line 196:27 - Line 208:23), packages/frontend/src/components/settings/UserProfileSection.tsx (Line 182:30 - Line 194:16)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn36" onclick="toggleCodeBlock('cloneGroup36', 'expandBtn36', 'collapseBtn36')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn36" onclick="toggleCodeBlock('cloneGroup36', 'expandBtn36', 'collapseBtn36')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup36"><code class="language-javascript text-sm text-gray-800">)} {...field} value={field.value ?? ''} /&gt;
                  &lt;/FormControl&gt;
                  &lt;FormMessage /&gt;
                &lt;/FormItem&gt;
              )}
            /&gt;
             {/* Organization Field */}
             &lt;FormField
              control={form.control}
              name=&quot;organization&quot;
              render={({ field }) =&gt; (
                &lt;FormItem&gt;
                  &lt;FormLabel&gt;{t('profile.organization'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/settings/UserProfileSection.tsx (Line 210:34 - Line 222:14), packages/frontend/src/components/settings/UserProfileSection.tsx (Line 182:30 - Line 194:16)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn37" onclick="toggleCodeBlock('cloneGroup37', 'expandBtn37', 'collapseBtn37')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn37" onclick="toggleCodeBlock('cloneGroup37', 'expandBtn37', 'collapseBtn37')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup37"><code class="language-javascript text-sm text-gray-800">)} {...field} value={field.value ?? ''} /&gt;
                  &lt;/FormControl&gt;
                  &lt;FormMessage /&gt;
                &lt;/FormItem&gt;
              )}
            /&gt;
             {/* Bio Field */}
             &lt;FormField
              control={form.control}
              name=&quot;bio&quot;
              render={({ field }) =&gt; (
                &lt;FormItem&gt;
                  &lt;FormLabel&gt;{t('profile.bio'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/ProjectImages.tsx (Line 127:2 - Line 154:2), packages/frontend/src/components/project/ProjectImages.tsx (Line 98:2 - Line 127:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn40" onclick="toggleCodeBlock('cloneGroup40', 'expandBtn40', 'collapseBtn40')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn40" onclick="toggleCodeBlock('cloneGroup40', 'expandBtn40', 'collapseBtn40')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup40"><code class="language-javascript text-sm text-gray-800">}
            /&gt;
          ))}
        &lt;/motion.div&gt;
      &lt;/&gt;
    );
  }

  return (
    &lt;&gt;
      {renderBatchActionsPanel()}
      &lt;motion.div
        className=&quot;space-y-2&quot;
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3 }}
      &gt;
        {images.map((image) =&gt; (
          &lt;ImageListItem
            key={image.id}
            image={image}
            onDelete={onDelete}
            onOpen={selectionMode ? undefined : onOpen}
            onResegment={handleResegment}
            selectionMode={selectionMode}
            isSelected={!!selectedImages[image.id]}
            onToggleSelection={(event) =&gt; onToggleSelection?.(image.id, event)}</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/project/ImageCard.tsx (Line 37:2 - Line 69:2), packages/frontend/src/components/project/ImageListItem.tsx (Line 110:2 - Line 137:13)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn42" onclick="toggleCodeBlock('cloneGroup42', 'expandBtn42', 'collapseBtn42')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn42" onclick="toggleCodeBlock('cloneGroup42', 'expandBtn42', 'collapseBtn42')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup42"><code class="language-javascript text-sm text-gray-800">}
      layout
    &gt;
      &lt;Card
        className={cn(
          &quot;overflow-hidden border-gray-200 dark:border-gray-700 transition-all group hover:shadow-md relative&quot;,
          isSelected ? &quot;ring-2 ring-blue-500&quot; : &quot;&quot;,
          className
        )}
        onClick={(e) =&gt; {
          if (selectionMode) {
            onToggleSelection?.(e);
          } else if (onOpen) {
            onOpen(image.id);
          }
        }}
      &gt;
        {/* Selection checkbox or actions */}
        {selectionMode ? (
          &lt;div className=&quot;absolute top-2 right-2 z-10&quot;&gt;
            &lt;div onClick={(e) =&gt; e.stopPropagation()}&gt;
              &lt;input
                type=&quot;checkbox&quot;
                checked={isSelected}
                onChange={(e) =&gt; onToggleSelection?.(e.nativeEvent)}
                className=&quot;h-5 w-5 rounded border-gray-300&quot;
              /&gt;
            &lt;/div&gt;
          &lt;/div&gt;
        ) : (
          &lt;ImageActions
            onDelete={() =&gt; onDelete(image.id)}
            onResegment={() =&gt; </code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/SignUp.tsx (Line 209:27 - Line 223:25), packages/frontend/src/pages/SignUp.tsx (Line 190:24 - Line 204:18)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn61" onclick="toggleCodeBlock('cloneGroup61', 'expandBtn61', 'collapseBtn61')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn61" onclick="toggleCodeBlock('cloneGroup61', 'expandBtn61', 'collapseBtn61')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup61"><code class="language-javascript text-sm text-gray-800">)}
                        {...field}
                        className=&quot;h-10 bg-gray-50 dark:bg-gray-700/50 border-gray-300 dark:border-gray-600 rounded-md&quot;
                      /&gt;
                    &lt;/FormControl&gt;
                    &lt;FormMessage /&gt;
                  &lt;/FormItem&gt;
                )}
              /&gt;
              &lt;FormField
                control={form.control}
                name=&quot;confirmPassword&quot;
                render={({ field }) =&gt; (
                  &lt;FormItem&gt;
                    &lt;FormLabel&gt;{t('common.passwordConfirm'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/SignUp.tsx (Line 228:2 - Line 241:2), packages/frontend/src/pages/SignUp.tsx (Line 209:2 - Line 203:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn62" onclick="toggleCodeBlock('cloneGroup62', 'expandBtn62', 'collapseBtn62')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn62" onclick="toggleCodeBlock('cloneGroup62', 'expandBtn62', 'collapseBtn62')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup62"><code class="language-javascript text-sm text-gray-800">t('auth.passwordPlaceholder')}
                        {...field}
                        className=&quot;h-10 bg-gray-50 dark:bg-gray-700/50 border-gray-300 dark:border-gray-600 rounded-md&quot;
                      /&gt;
                    &lt;/FormControl&gt;
                    &lt;FormMessage /&gt;
                  &lt;/FormItem&gt;
                )}
              /&gt;
              &lt;FormField
                control={form.control}
                name=&quot;agreeTerms&quot;
                render={({ field }) =&gt; (
                  &lt;FormItem </code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/pages/Profile.tsx (Line 367:6 - Line 417:11), packages/frontend/src/pages/Profile.tsx (Line 353:6 - Line 403:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn73" onclick="toggleCodeBlock('cloneGroup73', 'expandBtn73', 'collapseBtn73')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn73" onclick="toggleCodeBlock('cloneGroup73', 'expandBtn73', 'collapseBtn73')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup73"><code class="language-javascript text-sm text-gray-800">={{ animationDelay: &quot;-2s&quot; }} /&gt;
          &lt;div className=&quot;absolute top-2/3 left-1/3 w-40 h-40 bg-blue-400/20 dark:bg-blue-600/10 rounded-full filter blur-3xl animate-float&quot; style={{ animationDelay: &quot;-4s&quot; }} /&gt;
        &lt;/div&gt;
        &lt;div className=&quot;p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md&quot;&gt;
          {t('common.pleaseLogin')}
        &lt;/div&gt;
      &lt;/div&gt;
    );
  }

  if (!profileData) {
    return (
      &lt;div className=&quot;min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 relative flex justify-center items-center&quot;&gt;
        {/* Background elements */}
        &lt;div className=&quot;absolute inset-0 -z-10 pointer-events-none&quot;&gt;
          &lt;div className=&quot;absolute top-1/3 left-1/4 w-64 h-64 bg-blue-200/30 dark:bg-blue-400/10 rounded-full filter blur-3xl animate-float&quot; /&gt;
          &lt;div className=&quot;absolute bottom-1/4 right-1/3 w-80 h-80 bg-blue-300/20 dark:bg-blue-500/10 rounded-full filter blur-3xl animate-float&quot; style={{ animationDelay: &quot;-2s&quot; }} /&gt;
          &lt;div className=&quot;absolute top-2/3 left-1/3 w-40 h-40 bg-blue-400/20 dark:bg-blue-600/10 rounded-full filter blur-3xl animate-float&quot; style={{ animationDelay: &quot;-4s&quot; }} /&gt;
        &lt;/div&gt;
        &lt;div className=&quot;p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md text-red-500 dark:text-red-400&quot;&gt;
          {t('profile.fetchError')}
        &lt;/div&gt;
      &lt;/div&gt;
    );
  }

  const joinedDate = user.created_at ? new Date(user.created_at) : new Date();
  const month = joinedDate.toLocaleString('default', { month: 'long' });
  const year = joinedDate.getFullYear();
  const formattedJoinedDate = `${month} ${year}`;

  return (
    &lt;div className=&quot;flex flex-col min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 relative&quot;&gt;
      {/* Background elements */}
      &lt;div className=&quot;absolute inset-0 -z-10 pointer-events-none&quot;&gt;
        &lt;div className=&quot;absolute top-1/3 left-1/4 w-64 h-64 bg-blue-200/30 dark:bg-blue-400/10 rounded-full filter blur-3xl animate-float&quot; /&gt;
        &lt;div className=&quot;absolute bottom-1/4 right-1/3 w-80 h-80 bg-blue-300/20 dark:bg-blue-500/10 rounded-full filter blur-3xl animate-float&quot; style={{ animationDelay: &quot;-2s&quot; }} /&gt;
        &lt;div className=&quot;absolute top-2/3 left-1/3 w-40 h-40 bg-blue-400/20 dark:bg-blue-600/10 rounded-full filter blur-3xl animate-float&quot; style={{ animationDelay: &quot;-4s&quot; }} /&gt;
      &lt;/div&gt;

      &lt;DashboardHeader /&gt;
      &lt;main className=&quot;flex-1 p-4 md:p-6 lg:p-8 relative z-10&quot;&gt;
        &lt;h1 className=&quot;text-2xl font-bold mb-6&quot;&gt;{t('profile.pageTitle')}&lt;/h1&gt;
        &lt;div className=&quot;grid gap-8 lg:grid-cols-3&quot;&gt;
          &lt;Card className=&quot;lg:col-span-1&quot;&gt;
            &lt;CardContent className=&quot;p-6 text-center&quot;&gt;
              &lt;div className=&quot;relative w-24 h-24 mx-auto mb-4&quot;&gt;
                &lt;Avatar className=&quot;w-24 h-24 border-2 border-white dark:border-gray-800 shadow-md&quot;&gt;
                  {(avatarUrl || profileData.avatar_url) ? (
                    &lt;AvatarImage
                      src={avatarUrl </code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/ThemedFooter.tsx (Line 139:2 - Line 204:11), packages/frontend/src/components/ThemedFooter.tsx (Line 131:16 - Line 196:20)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn91" onclick="toggleCodeBlock('cloneGroup91', 'expandBtn91', 'collapseBtn91')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn91" onclick="toggleCodeBlock('cloneGroup91', 'expandBtn91', 'collapseBtn91')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup91"><code class="language-javascript text-sm text-gray-800">}
                aria-label=&quot;GitHub Repository&quot;
              &gt;
                &lt;Github className=&quot;w-5 h-5&quot; /&gt;
              &lt;/a&gt;
              &lt;a 
                href=&quot;mailto:<EMAIL>&quot; 
                className={getLinkHoverClasses()}
                aria-label=&quot;Contact Email&quot;
              &gt;
                &lt;Mail className=&quot;w-5 h-5&quot; /&gt;
              &lt;/a&gt;
            &lt;/div&gt;
          &lt;/div&gt;
          
          {/* Information Section */}
          &lt;div&gt;
            &lt;h3 className=&quot;text-lg font-semibold mb-6&quot;&gt;{getTranslation('footer.informationTitle')}&lt;/h3&gt;
            &lt;ul className=&quot;space-y-4&quot;&gt;
              &lt;li&gt;
                &lt;Link to=&quot;/documentation&quot; className={getLinkHoverClasses()}&gt;
                  {getTranslation('footer.documentationLink')}
                &lt;/Link&gt;
              &lt;/li&gt;
              &lt;li&gt;
                &lt;Link to=&quot;/terms-of-service&quot; className={getLinkHoverClasses()}&gt;
                  {getTranslation('footer.termsLink')}
                &lt;/Link&gt;
              &lt;/li&gt;
              &lt;li&gt;
                &lt;Link to=&quot;/privacy-policy&quot; className={getLinkHoverClasses()}&gt;
                  {getTranslation('footer.privacyLink')}
                &lt;/Link&gt;
              &lt;/li&gt;
              &lt;li&gt;
                &lt;Link to=&quot;/request-access&quot; className={getLinkHoverClasses()}&gt;
                  {getTranslation('footer.requestAccessLink') || &quot;Request Access&quot;}
                &lt;/Link&gt;
              &lt;/li&gt;
            &lt;/ul&gt;
          &lt;/div&gt;
          
          {/* Contact Section */}
          &lt;div&gt;
            &lt;h3 className=&quot;text-lg font-semibold mb-6&quot;&gt;{getTranslation('footer.contactTitle')}&lt;/h3&gt;
            &lt;ul className=&quot;space-y-4&quot;&gt;
              &lt;li&gt;
                &lt;a href=&quot;mailto:<EMAIL>&quot; className={getLinkHoverClasses()}&gt;
                  <EMAIL>
                &lt;/a&gt;
              &lt;/li&gt;
              &lt;li&gt;
                &lt;a href=&quot;https://www.fjfi.cvut.cz/&quot; target=&quot;_blank&quot; rel=&quot;noopener noreferrer&quot; className={getLinkHoverClasses()}&gt;
                  FNSPE CTU in Prague
                &lt;/a&gt;
              &lt;/li&gt;
              &lt;li&gt;
                &lt;a href=&quot;https://www.utia.cas.cz/&quot; target=&quot;_blank&quot; rel=&quot;noopener noreferrer&quot; className={getLinkHoverClasses()}&gt;
                  UTIA CAS
                &lt;/a&gt;
              &lt;/li&gt;
            &lt;/ul&gt;
          &lt;/div&gt;
        &lt;/div&gt;
        
        &lt;div className={`border-t </code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/ThemeToggle.tsx (Line 47:2 - Line 81:2), packages/frontend/src/components/settings/AppearanceSection.tsx (Line 64:6 - Line 105:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn92" onclick="toggleCodeBlock('cloneGroup92', 'expandBtn92', 'collapseBtn92')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn92" onclick="toggleCodeBlock('cloneGroup92', 'expandBtn92', 'collapseBtn92')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup92"><code class="language-javascript text-sm text-gray-800">}&gt;
            &lt;MonitorSmartphone className=&quot;h-4 w-4 mr-2&quot; /&gt;
            {t('settings.system')}
          &lt;/DropdownMenuItem&gt;
        &lt;/DropdownMenuContent&gt;
      &lt;/DropdownMenu&gt;
    );
  }

  // Default variant shows three buttons
  return (
    &lt;div className=&quot;flex items-center space-x-2&quot;&gt;
      &lt;Button
        variant={theme === 'light' ? 'default' : 'outline'}
        size=&quot;sm&quot;
        className=&quot;w-24&quot;
        onClick={() =&gt; handleThemeChange('light')}
      &gt;
        &lt;Sun className=&quot;h-4 w-4 mr-2&quot; /&gt;
        {t('settings.light')}
      &lt;/Button&gt;
      &lt;Button
        variant={theme === 'dark' ? 'default' : 'outline'}
        size=&quot;sm&quot;
        className=&quot;w-24&quot;
        onClick={() =&gt; handleThemeChange('dark')}
      &gt;
        &lt;Moon className=&quot;h-4 w-4 mr-2&quot; /&gt;
        {t('settings.dark')}
      &lt;/Button&gt;
      &lt;Button
        variant={theme === 'system' ? 'default' : 'outline'}
        size=&quot;sm&quot;
        className=&quot;w-24&quot;
        onClick={() =&gt; handleThemeChange('system')}</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/NewProject.tsx (Line 92:20 - Line 119:11), packages/frontend/src/components/project/ProjectDialogForm.tsx (Line 38:2 - Line 70:12)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn94" onclick="toggleCodeBlock('cloneGroup94', 'expandBtn94', 'collapseBtn94')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn94" onclick="toggleCodeBlock('cloneGroup94', 'expandBtn94', 'collapseBtn94')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup94"><code class="language-javascript text-sm text-gray-800">}&gt;
          &lt;div className=&quot;grid gap-4 py-4&quot;&gt;
            &lt;div className=&quot;space-y-2&quot;&gt;
              &lt;Label htmlFor=&quot;projectName&quot; className=&quot;text-right&quot;&gt;
                {t('common.projectName')}
              &lt;/Label&gt;
              &lt;Input
                id=&quot;projectName&quot;
                placeholder={t('projects.projectNamePlaceholder')}
                value={projectName}
                onChange={(e) =&gt; setProjectName(e.target.value)}
                required
              /&gt;
            &lt;/div&gt;
            &lt;div className=&quot;space-y-2&quot;&gt;
              &lt;Label htmlFor=&quot;projectDescription&quot; className=&quot;text-right&quot;&gt;
                {t('common.description')} ({t('common.optional')})
              &lt;/Label&gt;
              &lt;Input
                id=&quot;projectDescription&quot;
                placeholder={t('projects.projectDescPlaceholder')}
                value={projectDescription}
                onChange={(e) =&gt; setProjectDescription(e.target.value)}
              /&gt;
            &lt;/div&gt;
          &lt;/div&gt;
          &lt;DialogFooter&gt;
            &lt;Button type=&quot;submit&quot; disabled={isCreating</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/Navbar.tsx (Line 120:2 - Line 153:2), packages/frontend/src/components/Navbar.tsx (Line 108:3 - Line 148:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn96" onclick="toggleCodeBlock('cloneGroup96', 'expandBtn96', 'collapseBtn96')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn96" onclick="toggleCodeBlock('cloneGroup96', 'expandBtn96', 'collapseBtn96')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup96"><code class="language-javascript text-sm text-gray-800">}
            &gt;
              {t('navbar.home')}
            &lt;/Link&gt;
            &lt;Link
              to=&quot;/documentation&quot;
              className=&quot;text-gray-700 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 py-2 transition-colors&quot;
              onClick={() =&gt; setIsMobileMenuOpen(false)}
            &gt;
              {t('navbar.documentation')}
            &lt;/Link&gt;
            &lt;Link
              to=&quot;/terms-of-service&quot;
              className=&quot;text-gray-700 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 py-2 transition-colors&quot;
              onClick={() =&gt; setIsMobileMenuOpen(false)}
            &gt;
              {t('navbar.terms')}
            &lt;/Link&gt;
            &lt;Link
              to=&quot;/privacy-policy&quot;
              className=&quot;text-gray-700 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 py-2 transition-colors&quot;
              onClick={() =&gt; setIsMobileMenuOpen(false)}
            &gt;
              {t('navbar.privacy')}
            &lt;/Link&gt;
            &lt;Link
              to=&quot;/sign-in&quot;
              className=&quot;text-gray-700 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 py-2 transition-colors&quot;
              onClick={() =&gt; setIsMobileMenuOpen(false)}
            &gt;
              {t('navbar.login')}
            &lt;/Link&gt;
            &lt;Button asChild className=&quot;w-full rounded-md&quot;&gt;
              &lt;Link to=&quot;/request-access&quot; onClick={() =&gt; setIsMobileMenuOpen(false)}</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/components/DashboardActions.tsx (Line 37:8 - Line 48:2), packages/frontend/src/components/project/ProjectToolbar.tsx (Line 168:8 - Line 183:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn99" onclick="toggleCodeBlock('cloneGroup99', 'expandBtn99', 'collapseBtn99')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn99" onclick="toggleCodeBlock('cloneGroup99', 'expandBtn99', 'collapseBtn99')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup99"><code class="language-javascript text-sm text-gray-800">={viewMode === &quot;grid&quot; ? &quot;default&quot; : &quot;ghost&quot;}
          size=&quot;sm&quot; 
          className=&quot;h-9 px-2.5 rounded-r-none&quot;
          onClick={() =&gt; setViewMode(&quot;grid&quot;)}
        &gt;
          &lt;Grid2X2 className=&quot;h-4 w-4&quot; /&gt;
        &lt;/Button&gt;
        &lt;Button 
          variant={viewMode === &quot;list&quot; ? &quot;default&quot; : &quot;ghost&quot;}
          size=&quot;sm&quot; 
          className=&quot;h-9 px-2.5 rounded-l-none&quot;
          onClick={() =&gt; setViewMode(&quot;list&quot;)}</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/routes/direct-endpoints.js (Line 6:14 - Line 17:46), packages/backend/src/routes/health.js (Line 7:4 - Line 18:7)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn131" onclick="toggleCodeBlock('cloneGroup131', 'expandBtn131', 'collapseBtn131')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn131" onclick="toggleCodeBlock('cloneGroup131', 'expandBtn131', 'collapseBtn131')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup131"><code class="language-javascript text-sm text-gray-800">, (req, res) =&gt; {
    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      components: {
        api: 'healthy',
        database: 'connected'
      }
    });
  });

  // Add simple mock login endpoint for testing</code></pre></div><div class="py-4"><p class="text-gray-600">packages/frontend/src/App.tsx (Line 266:14 - Line 280:17), packages/frontend/src/App.tsx (Line 258:10 - Line 273:18)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn144" onclick="toggleCodeBlock('cloneGroup144', 'expandBtn144', 'collapseBtn144')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn144" onclick="toggleCodeBlock('cloneGroup144', 'expandBtn144', 'collapseBtn144')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup144"><code class="language-javascript text-sm text-gray-800"> /&gt;
        &lt;/ErrorBoundary&gt;
      &lt;/ProtectedRoute&gt;
    } /&gt;
    {/* Ensure both URL formats work properly */}
    &lt;Route path=&quot;/projects/:id&quot; element={
      &lt;ProtectedRoute&gt;
        &lt;ErrorBoundary componentName=&quot;ProjectDetailPage&quot;&gt;
          &lt;ProjectDetail /&gt;
        &lt;/ErrorBoundary&gt;
      &lt;/ProtectedRoute&gt;
    } /&gt;
    &lt;Route path=&quot;/projects/:projectId/segmentation/:imageId&quot; element={
      &lt;ProtectedRoute&gt;
        &lt;ErrorBoundary componentName=&quot;SegmentationPage</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/simple-server.js (Line 99:17 - Line 109:24), packages/backend/src/simple-server.js (Line 52:14 - Line 62:17)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn145" onclick="toggleCodeBlock('cloneGroup145', 'expandBtn145', 'collapseBtn145')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn145" onclick="toggleCodeBlock('cloneGroup145', 'expandBtn145', 'collapseBtn145')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup145"><code class="language-javascript text-sm text-gray-800">) {
    // Parse request body
    let body = '';
    req.on('data', chunk =&gt; {
      body += chunk.toString();
    });

    req.on('end', () =&gt; {
      try {
        const data = JSON.parse(body);
        console.log('Registration attempt:'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/simple-server.js (Line 131:11 - Line 143:76), packages/backend/src/simple-server.js (Line 86:11 - Line 98:71)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn146" onclick="toggleCodeBlock('cloneGroup146', 'expandBtn146', 'collapseBtn146')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn146" onclick="toggleCodeBlock('cloneGroup146', 'expandBtn146', 'collapseBtn146')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup146"><code class="language-javascript text-sm text-gray-800">}));
        }
      } catch (error) {
        res.statusCode = 400;
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify({
          error: 'Invalid request body',
          message: error.message
        }));
      }
    });
  }
  // Handle refresh token endpoint - support multiple paths for compatibility</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/simple-server.js (Line 144:16 - Line 154:24), packages/backend/src/simple-server.js (Line 52:14 - Line 62:17)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn147" onclick="toggleCodeBlock('cloneGroup147', 'expandBtn147', 'collapseBtn147')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn147" onclick="toggleCodeBlock('cloneGroup147', 'expandBtn147', 'collapseBtn147')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup147"><code class="language-javascript text-sm text-gray-800">) {
    // Parse request body
    let body = '';
    req.on('data', chunk =&gt; {
      body += chunk.toString();
    });

    req.on('end', () =&gt; {
      try {
        const data = JSON.parse(body);
        console.log('Token refresh attempt'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/simple-server.js (Line 161:13 - Line 169:28), packages/backend/src/simple-server.js (Line 122:13 - Line 130:34)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn148" onclick="toggleCodeBlock('cloneGroup148', 'expandBtn148', 'collapseBtn148')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn148" onclick="toggleCodeBlock('cloneGroup148', 'expandBtn148', 'collapseBtn148')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup148"><code class="language-javascript text-sm text-gray-800">accessToken: &quot;mock-access-token-&quot; + Date.now(),
            refreshToken: &quot;mock-refresh-token-&quot; + Date.now(),
            tokenType: &quot;Bearer&quot;
          }));
        } else {
          res.statusCode = 400;
          res.setHeader('Content-Type', 'application/json');
          res.end(JSON.stringify({
            message: &quot;Refresh token is required&quot;</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/simple-server.js (Line 170:11 - Line 182:69), packages/backend/src/simple-server.js (Line 86:11 - Line 98:71)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn149" onclick="toggleCodeBlock('cloneGroup149', 'expandBtn149', 'collapseBtn149')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn149" onclick="toggleCodeBlock('cloneGroup149', 'expandBtn149', 'collapseBtn149')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup149"><code class="language-javascript text-sm text-gray-800">}));
        }
      } catch (error) {
        res.statusCode = 400;
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify({
          error: 'Invalid request body',
          message: error.message
        }));
      }
    });
  }
  // Handle logout endpoint - support multiple paths for compatibility</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/simple-server.js (Line 183:15 - Line 193:17), packages/backend/src/simple-server.js (Line 52:14 - Line 62:17)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn150" onclick="toggleCodeBlock('cloneGroup150', 'expandBtn150', 'collapseBtn150')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn150" onclick="toggleCodeBlock('cloneGroup150', 'expandBtn150', 'collapseBtn150')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup150"><code class="language-javascript text-sm text-gray-800">) {
    // Parse request body
    let body = '';
    req.on('data', chunk =&gt; {
      body += chunk.toString();
    });

    req.on('end', () =&gt; {
      try {
        const data = JSON.parse(body);
        console.log('Logout attempt'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/simple-server.js (Line 381:7 - Line 391:30), packages/backend/src/simple-server.js (Line 52:14 - Line 62:17)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn151" onclick="toggleCodeBlock('cloneGroup151', 'expandBtn151', 'collapseBtn151')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn151" onclick="toggleCodeBlock('cloneGroup151', 'expandBtn151', 'collapseBtn151')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup151"><code class="language-javascript text-sm text-gray-800">) {
    // Parse request body
    let body = '';
    req.on('data', chunk =&gt; {
      body += chunk.toString();
    });

    req.on('end', () =&gt; {
      try {
        const data = JSON.parse(body);
        console.log('Creating project with data:'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/simple-server.js (Line 434:11 - Line 445:25), packages/backend/src/simple-server.js (Line 200:2 - Line 211:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn152" onclick="toggleCodeBlock('cloneGroup152', 'expandBtn152', 'collapseBtn152')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn152" onclick="toggleCodeBlock('cloneGroup152', 'expandBtn152', 'collapseBtn152')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup152"><code class="language-javascript text-sm text-gray-800">));
      } catch (error) {
        res.statusCode = 400;
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify({
          error: 'Invalid request body',
          message: error.message
        }));
      }
    });
  }
  // Handle DELETE project</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/simple-server.js (Line 491:9 - Line 499:4), packages/backend/src/simple-server.js (Line 256:9 - Line 263:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn153" onclick="toggleCodeBlock('cloneGroup153', 'expandBtn153', 'collapseBtn153')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn153" onclick="toggleCodeBlock('cloneGroup153', 'expandBtn153', 'collapseBtn153')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup153"><code class="language-javascript text-sm text-gray-800">// Create the directory if it doesn't exist
        const mockDataDir = path.join(__dirname, 'mock-data');
        if (!fs.existsSync(mockDataDir)) {
          fs.mkdirSync(mockDataDir, { recursive: true });
        }
        // Create an empty projects file
        fs.writeFileSync(projectsPath, JSON.stringify([]));

        res</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/simple-server.js (Line 519:5 - Line 527:39), packages/backend/src/simple-server.js (Line 54:5 - Line 62:17)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn154" onclick="toggleCodeBlock('cloneGroup154', 'expandBtn154', 'collapseBtn154')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn154" onclick="toggleCodeBlock('cloneGroup154', 'expandBtn154', 'collapseBtn154')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup154"><code class="language-javascript text-sm text-gray-800">let body = '';
    req.on('data', chunk =&gt; {
      body += chunk.toString();
    });

    req.on('end', () =&gt; {
      try {
        const data = JSON.parse(body);
        console.log('Received batch segmentation request:'</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/simple-server.js (Line 537:9 - Line 548:29), packages/backend/src/simple-server.js (Line 200:9 - Line 211:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn155" onclick="toggleCodeBlock('cloneGroup155', 'expandBtn155', 'collapseBtn155')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn155" onclick="toggleCodeBlock('cloneGroup155', 'expandBtn155', 'collapseBtn155')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup155"><code class="language-javascript text-sm text-gray-800">}));
      } catch (error) {
        res.statusCode = 400;
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify({
          error: 'Invalid request body',
          message: error.message
        }));
      }
    });
  }
  // Handle Socket.IO endpoint</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/simple-server.js (Line 738:7 - Line 747:21), packages/backend/src/simple-server.js (Line 708:2 - Line 717:44)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn156" onclick="toggleCodeBlock('cloneGroup156', 'expandBtn156', 'collapseBtn156')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn156" onclick="toggleCodeBlock('cloneGroup156', 'expandBtn156', 'collapseBtn156')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup156"><code class="language-javascript text-sm text-gray-800">) {
      // Extract session ID from URL
      const sidMatch = pathname.match(/\/socket\.io\/([^/]+)\/polling\//);
      const sid = sidMatch ? sidMatch[1] : null;

      if (sid &amp;&amp; socketSessions.has(sid)) {
        const session = socketSessions.get(sid);
        session.lastActivity = Date.now();

        // Read request body</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/simple-server.fix.js (Line 1:1 - Line 278:2), packages/backend/src/simple-server.js (Line 1:1 - Line 278:3)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn157" onclick="toggleCodeBlock('cloneGroup157', 'expandBtn157', 'collapseBtn157')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn157" onclick="toggleCodeBlock('cloneGroup157', 'expandBtn157', 'collapseBtn157')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup157"><code class="language-javascript text-sm text-gray-800">// Simple HTTP server without dependencies
const http = require('http');
const fs = require('fs');
const path = require('path');

// Create HTTP server
const server = http.createServer((req, res) =&gt; {
  console.log(`${req.method} ${req.url}`);

  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.statusCode = 204;
    res.end();
    return;
  }

  // Parse URL
  const url = new URL(req.url, `http://${req.headers.host}`);
  const pathname = url.pathname;

  // Handle API endpoints
  if (pathname === '/api/health') {
    res.setHeader('Content-Type', 'application/json');
    res.statusCode = 200;
    res.end(JSON.stringify({
      status: &quot;OK&quot;,
      timestamp: new Date().toISOString(),
      service: &quot;backend-simple&quot;
    }));
  }
  else if (pathname === '/api/status') {
    res.setHeader('Content-Type', 'application/json');
    res.statusCode = 200;
    res.end(JSON.stringify({
      status: &quot;running&quot;,
      version: &quot;1.0.0&quot;,
      environment: process.env.NODE_ENV || &quot;development&quot;,
      uptime: process.uptime()
    }));
  }
  else if (pathname === '/api/metrics/performance') {
    res.setHeader('Content-Type', 'application/json');
    res.statusCode = 200;
    res.end(JSON.stringify({ success: true }));
  }
  // Support multiple paths for login (compatibility with standard Express router)
  else if (pathname === '/api/auth/login' || pathname === '/auth/login') {
    // Parse request body
    let body = '';
    req.on('data', chunk =&gt; {
      body += chunk.toString();
    });

    req.on('end', () =&gt; {
      try {
        const data = JSON.parse(body);
        console.log('Login attempt:', data.email);

        // Simple mock authentication
        if (data.email &amp;&amp; data.password) {
          res.setHeader('Content-Type', 'application/json');
          res.statusCode = 200;
          res.end(JSON.stringify({
            user: {
              id: &quot;user-12bprusek-gym-nymburk-cz&quot;,
              email: data.email,
              name: &quot;Michal Průšek&quot;,
              created_at: new Date().toISOString(),
              preferred_language: &quot;en&quot;,
              theme_preference: &quot;dark&quot;
            },
            accessToken: &quot;mock-access-token-&quot; + Date.now(),
            refreshToken: &quot;mock-refresh-token-&quot; + Date.now(),
            tokenType: &quot;Bearer&quot;
          }));
        } else {
          res.statusCode = 401;
          res.setHeader('Content-Type', 'application/json');
          res.end(JSON.stringify({
            message: &quot;Invalid email or password&quot;
          }));
        }
      } catch (error) {
        res.statusCode = 400;
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify({
          error: 'Invalid request body',
          message: error.message
        }));
      }
    });
  }
  // Handle register endpoint - support multiple paths for compatibility
  else if (pathname === '/api/auth/register' || pathname === '/auth/register') {
    // Parse request body
    let body = '';
    req.on('data', chunk =&gt; {
      body += chunk.toString();
    });

    req.on('end', () =&gt; {
      try {
        const data = JSON.parse(body);
        console.log('Registration attempt:', data.email);

        // Simple mock registration
        if (data.email &amp;&amp; data.password) {
          res.setHeader('Content-Type', 'application/json');
          res.statusCode = 201;
          res.end(JSON.stringify({
            user: {
              id: &quot;user-&quot; + data.email.replace(/[^a-zA-Z0-9]/g, '-'),
              email: data.email,
              name: data.name || data.email.split('@')[0],
              created_at: new Date().toISOString()
            },
            accessToken: &quot;mock-access-token-&quot; + Date.now(),
            refreshToken: &quot;mock-refresh-token-&quot; + Date.now(),
            tokenType: &quot;Bearer&quot;
          }));
        } else {
          res.statusCode = 400;
          res.setHeader('Content-Type', 'application/json');
          res.end(JSON.stringify({
            message: &quot;Email and password are required&quot;
          }));
        }
      } catch (error) {
        res.statusCode = 400;
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify({
          error: 'Invalid request body',
          message: error.message
        }));
      }
    });
  }
  // Handle refresh token endpoint - support multiple paths for compatibility
  else if (pathname === '/api/auth/refresh' || pathname === '/auth/refresh') {
    // Parse request body
    let body = '';
    req.on('data', chunk =&gt; {
      body += chunk.toString();
    });

    req.on('end', () =&gt; {
      try {
        const data = JSON.parse(body);
        console.log('Token refresh attempt');

        // Simple mock token refresh
        if (data.refreshToken) {
          res.setHeader('Content-Type', 'application/json');
          res.statusCode = 200;
          res.end(JSON.stringify({
            accessToken: &quot;mock-access-token-&quot; + Date.now(),
            refreshToken: &quot;mock-refresh-token-&quot; + Date.now(),
            tokenType: &quot;Bearer&quot;
          }));
        } else {
          res.statusCode = 400;
          res.setHeader('Content-Type', 'application/json');
          res.end(JSON.stringify({
            message: &quot;Refresh token is required&quot;
          }));
        }
      } catch (error) {
        res.statusCode = 400;
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify({
          error: 'Invalid request body',
          message: error.message
        }));
      }
    });
  }
  // Handle logout endpoint - support multiple paths for compatibility
  else if (pathname === '/api/auth/logout' || pathname === '/auth/logout') {
    // Parse request body
    let body = '';
    req.on('data', chunk =&gt; {
      body += chunk.toString();
    });

    req.on('end', () =&gt; {
      try {
        const data = JSON.parse(body);
        console.log('Logout attempt');

        // Simple mock logout
        res.setHeader('Content-Type', 'application/json');
        res.statusCode = 200;
        res.end(JSON.stringify({
          message: &quot;Logged out successfully&quot;
        }));
      } catch (error) {
        res.statusCode = 400;
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify({
          error: 'Invalid request body',
          message: error.message
        }));
      }
    });
  }
  else if (pathname === '/api/users/me' || pathname === '/users/me') {
    res.setHeader('Content-Type', 'application/json');
    res.statusCode = 200;
    res.end(JSON.stringify({
      id: &quot;user-12bprusek-gym-nymburk-cz&quot;,
      email: &quot;<EMAIL>&quot;,
      name: &quot;Michal Průšek&quot;,
      role: &quot;admin&quot;,
      created_at: new Date().toISOString(),
      profile: {
        preferred_language: &quot;en&quot;,
        theme_preference: &quot;dark&quot;
      }
    }));
  }
  else if (pathname === '/api/users/me/statistics' || pathname === '/api/users/me/stats') {
    res.setHeader('Content-Type', 'application/json');
    res.statusCode = 200;
    res.end(JSON.stringify({
      projects_count: 5,
      images_count: 120,
      segmentations_count: 98,
      last_login: new Date().toISOString()
    }));
  }
  else if (pathname === '/api/projects' &amp;&amp; req.method === 'GET') {
    // Parse query parameters
    const limit = parseInt(url.searchParams.get('limit')) || 10;
    const offset = parseInt(url.searchParams.get('offset')) || 0;

    // Load projects from file
    let projects = [];
    try {
      const fs = require('fs');
      const path = require('path');
      const projectsPath = path.join(__dirname, 'mock-data', 'projects.json');
      if (fs.existsSync(projectsPath)) {
        try {
          projects = JSON.parse(fs.readFileSync(projectsPath, 'utf8'));
          console.log(`Loaded ${projects.length} projects`);
        } catch (parseError) {
          console.error('Error parsing projects file:', parseError);
        }
      } else {
        console.log('Projects file not found, creating empty file');
        // Create the directory if it doesn't exist
        const mockDataDir = path.join(__dirname, 'mock-data');
        if (!fs.existsSync(mockDataDir)) {
          fs.mkdirSync(mockDataDir, { recursive: true });
        }
        // Create an empty projects file
        fs.writeFileSync(projectsPath, JSON.stringify([]));
      }
    } catch (error) {
      console.error('Error loading projects:', error);
    }

    res.setHeader('Content-Type', 'application/json');
    res.statusCode = 200;
    res.end(JSON.stringify({
      projects,
      total: projects.length,
      limit,
      offset
    }));
  }
  // Handle specific project details
  else if (pathname.match(/^\/api\/projects\/project-/))</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/simple-server.fix.js (Line 278:2 - Line 445:38), packages/backend/src/simple-server.js (Line 278:6 - Line 211:5)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn158" onclick="toggleCodeBlock('cloneGroup158', 'expandBtn158', 'collapseBtn158')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn158" onclick="toggleCodeBlock('cloneGroup158', 'expandBtn158', 'collapseBtn158')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup158"><code class="language-javascript text-sm text-gray-800">) {
    const projectId = pathname.split('/').pop();

    // Try to find project in data
    let project = null;
    try {
      const fs = require('fs');
      const path = require('path');
      const projectsPath = path.join(__dirname, 'mock-data', 'projects.json');
      if (fs.existsSync(projectsPath)) {
        try {
          const projects = JSON.parse(fs.readFileSync(projectsPath, 'utf8'));
          project = projects.find(p =&gt; p.id === projectId);
          console.log(`Looking for project ${projectId}, found: ${project ? 'yes' : 'no'}`);
        } catch (parseError) {
          console.error('Error parsing projects file:', parseError);
        }
      }
    } catch (error) {
      console.error('Error finding project:', error);
    }

    if (project) {
      res.setHeader('Content-Type', 'application/json');
      res.statusCode = 200;
      res.end(JSON.stringify(project));
    } else {
      res.setHeader('Content-Type', 'application/json');
      res.statusCode = 404;
      res.end(JSON.stringify({
        error: &quot;Project not found&quot;,
        message: &quot;The requested project does not exist&quot;
      }));
    }
  }
  // Handle queue status
  else if (pathname.match(/^\/api\/queue-status\/project-\d+$/)) {
    const projectId = pathname.split('/').pop();

    res.setHeader('Content-Type', 'application/json');
    res.statusCode = 200;
    res.end(JSON.stringify({
      project_id: projectId,
      total_images: 15,
      processed_images: 12,
      pending_images: 3,
      failed_images: 0,
      status: &quot;processing&quot;,
      last_updated: new Date().toISOString()
    }));
  }
  // Handle project images
  else if (pathname.match(/^\/api\/projects\/project-\d+\/images$/)) {
    const projectId = pathname.split('/')[3];

    // Create mock images for project
    const mockImages = [
      {
        id: `image-1-${projectId}`,
        project_id: projectId,
        name: &quot;Sample Image 1&quot;,
        file_path: &quot;/uploads/sample1.jpg&quot;,
        thumbnail_path: &quot;/uploads/sample1_thumb.jpg&quot;,
        width: 800,
        height: 600,
        file_size: 120000,
        mime_type: &quot;image/jpeg&quot;,
        created_at: new Date().toISOString(),
        status: &quot;processed&quot;
      },
      {
        id: `image-2-${projectId}`,
        project_id: projectId,
        name: &quot;Sample Image 2&quot;,
        file_path: &quot;/uploads/sample2.jpg&quot;,
        thumbnail_path: &quot;/uploads/sample2_thumb.jpg&quot;,
        width: 1024,
        height: 768,
        file_size: 150000,
        mime_type: &quot;image/jpeg&quot;,
        created_at: new Date().toISOString(),
        status: &quot;processed&quot;
      },
      {
        id: `image-3-${projectId}`,
        project_id: projectId,
        name: &quot;Sample Image 3&quot;,
        file_path: &quot;/uploads/sample3.jpg&quot;,
        thumbnail_path: &quot;/uploads/sample3_thumb.jpg&quot;,
        width: 1280,
        height: 720,
        file_size: 180000,
        mime_type: &quot;image/jpeg&quot;,
        created_at: new Date().toISOString(),
        status: &quot;processed&quot;
      }
    ];

    res.setHeader('Content-Type', 'application/json');
    res.statusCode = 200;
    res.end(JSON.stringify(mockImages));
  }
  // Handle API POST to /api/projects
  else if (pathname === '/api/projects' &amp;&amp; req.method === 'POST') {
    // Parse request body
    let body = '';
    req.on('data', chunk =&gt; {
      body += chunk.toString();
    });

    req.on('end', () =&gt; {
      try {
        const data = JSON.parse(body);
        console.log('Creating project with data:', data);

        const newProject = {
          id: `project-${Date.now()}`,
          name: data.name,
          description: data.description || '',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          user_id: &quot;user-12bprusek-gym-nymburk-cz&quot;,
          status: &quot;active&quot;,
          image_count: 0
        };

        // Try to save to mock projects file
        try {
          const fs = require('fs');
          const path = require('path');
          const mockDataDir = path.join(__dirname, 'mock-data');
          const mockProjectsPath = path.join(mockDataDir, 'projects.json');
          let projects = [];

          if (fs.existsSync(mockProjectsPath)) {
            try {
              projects = JSON.parse(fs.readFileSync(mockProjectsPath, 'utf8'));
              console.log(`Loaded ${projects.length} existing projects`);
            } catch (parseError) {
              console.error('Error parsing projects file, starting with empty projects:', parseError);
              projects = [];
            }
          } else {
            console.log('Projects file not found, creating new file');
            fs.mkdirSync(path.dirname(mockProjectsPath), { recursive: true });
          }

          projects.push(newProject);
          fs.writeFileSync(mockProjectsPath, JSON.stringify(projects, null, 2));
          console.log(`Added new project to data: ${newProject.id}`);
        } catch (error) {
          console.error('Error saving project to data:', error);
        }

        res.setHeader('Content-Type', 'application/json');
        res.statusCode = 201;
        res.end(JSON.stringify(newProject));
      } catch (error) {
        res.statusCode = 400;
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify({
          error: 'Invalid request body',
          message: error.message
        }));
      }
    });
  }
  // Handle batch segmentation endpoint</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/simple-server.fix.js (Line 444:3 - Line 508:25), packages/backend/src/simple-server.js (Line 515:3 - Line 579:34)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn159" onclick="toggleCodeBlock('cloneGroup159', 'expandBtn159', 'collapseBtn159')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn159" onclick="toggleCodeBlock('cloneGroup159', 'expandBtn159', 'collapseBtn159')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup159"><code class="language-javascript text-sm text-gray-800">}
  // Handle batch segmentation endpoint
  else if (pathname === '/images/segmentation/trigger-batch' || pathname === '/api/images/segmentation/trigger-batch') {
    // Parse request body for batch segmentation
    let body = '';
    req.on('data', chunk =&gt; {
      body += chunk.toString();
    });

    req.on('end', () =&gt; {
      try {
        const data = JSON.parse(body);
        console.log('Received batch segmentation request:', data);

        res.setHeader('Content-Type', 'application/json');
        res.statusCode = 200;
        res.end(JSON.stringify({
          success: true,
          message: 'Batch segmentation triggered successfully',
          jobId: 'mock-job-' + Date.now(),
          imageIds: data.imageIds || [],
          timestamp: new Date().toISOString()
        }));
      } catch (error) {
        res.statusCode = 400;
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify({
          error: 'Invalid request body',
          message: error.message
        }));
      }
    });
  }
  // Handle Socket.IO endpoint
  else if (pathname === '/socket.io' || pathname.startsWith('/socket.io/')) {
    handleSocketIORequest(req, res);
  }
  // Handle static files from uploads directory
  else if (pathname.startsWith('/uploads/')) {
    const filePath = path.join(__dirname, '..', pathname);

    // Check if file exists
    fs.access(filePath, fs.constants.F_OK, (err) =&gt; {
      if (err) {
        res.statusCode = 404;
        res.end(JSON.stringify({ error: &quot;File not found&quot; }));
        return;
      }

      // Determine content type
      let contentType = 'application/octet-stream';
      if (filePath.endsWith('.jpg') || filePath.endsWith('.jpeg')) {
        contentType = 'image/jpeg';
      } else if (filePath.endsWith('.png')) {
        contentType = 'image/png';
      } else if (filePath.endsWith('.json')) {
        contentType = 'application/json';
      }

      // Stream the file
      res.setHeader('Content-Type', contentType);
      fs.createReadStream(filePath).pipe(res);
    });
  }
  // Handle DELETE project</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/simple-server.fix.js (Line 506:5 - Line 579:43), packages/backend/src/simple-server.js (Line 443:5 - Line 516:38)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn160" onclick="toggleCodeBlock('cloneGroup160', 'expandBtn160', 'collapseBtn160')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn160" onclick="toggleCodeBlock('cloneGroup160', 'expandBtn160', 'collapseBtn160')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup160"><code class="language-javascript text-sm text-gray-800">});
  }
  // Handle DELETE project
  else if (pathname.match(/^\/api\/projects\/project-/) &amp;&amp; req.method === 'DELETE') {
    const projectId = pathname.split('/').pop();
    console.log(`Deleting project with ID: ${projectId}`);

    try {
      const fs = require('fs');
      const path = require('path');
      const projectsPath = path.join(__dirname, 'mock-data', 'projects.json');

      if (fs.existsSync(projectsPath)) {
        try {
          let projects = JSON.parse(fs.readFileSync(projectsPath, 'utf8'));
          const initialLength = projects.length;

          // Find the project index
          const projectIndex = projects.findIndex(p =&gt; p.id === projectId);

          if (projectIndex !== -1) {
            // Remove the project
            projects.splice(projectIndex, 1);
            fs.writeFileSync(projectsPath, JSON.stringify(projects, null, 2));
            console.log(`Project ${projectId} deleted. Projects remaining: ${projects.length}`);

            // Return 204 status code (success with no content) to match the real API
            res.statusCode = 204;
            res.end();
          } else {
            console.error(`Project ${projectId} not found for deletion`);
            res.statusCode = 404;
            res.setHeader('Content-Type', 'application/json');
            res.end(JSON.stringify({
              error: &quot;Project not found&quot;,
              message: &quot;The requested project does not exist or has already been deleted&quot;
            }));
          }
        } catch (parseError) {
          console.error('Error parsing projects file for deletion:', parseError);
          res.statusCode = 500;
          res.setHeader('Content-Type', 'application/json');
          res.end(JSON.stringify({
            error: &quot;Internal server error&quot;,
            message: &quot;Error reading project data&quot;
          }));
        }
      } else {
        // Create the directory if it doesn't exist
        const mockDataDir = path.join(__dirname, 'mock-data');
        if (!fs.existsSync(mockDataDir)) {
          fs.mkdirSync(mockDataDir, { recursive: true });
        }
        // Create an empty projects file
        fs.writeFileSync(projectsPath, JSON.stringify([]));

        res.statusCode = 404;
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify({
          error: &quot;Project not found&quot;,
          message: &quot;No projects data available&quot;
        }));
      }
    } catch (error) {
      console.error('Error deleting project:', error);
      res.statusCode = 500;
      res.setHeader('Content-Type', 'application/json');
      res.end(JSON.stringify({
        error: &quot;Internal server error&quot;,
        message: error.message
      }));
    }
  }
  // Add support for project images endpoint</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/simple-server.fix.js (Line 580:3 - Line 808:2), packages/backend/src/simple-server.js (Line 580:3 - Line 808:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn161" onclick="toggleCodeBlock('cloneGroup161', 'expandBtn161', 'collapseBtn161')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn161" onclick="toggleCodeBlock('cloneGroup161', 'expandBtn161', 'collapseBtn161')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup161"><code class="language-javascript text-sm text-gray-800">else if (pathname.match(/^\/api\/projects\/project-.*\/images$/)) {
    const projectId = pathname.split('/')[3]; // Extract project ID from URL

    // Try to find project
    let project = null;
    try {
      const fs = require('fs');
      const path = require('path');
      const projectsPath = path.join(__dirname, 'mock-data', 'projects.json');
      if (fs.existsSync(projectsPath)) {
        const projects = JSON.parse(fs.readFileSync(projectsPath, 'utf8'));
        project = projects.find(p =&gt; p.id === projectId);
      }
    } catch (error) {
      console.error('Error finding project for images:', error);
    }

    if (project) {
      // Return sample images for the project
      const sampleImages = Array.from({ length: project.image_count || 0 }).map((_, index) =&gt; ({
        id: `image-${index + 1}-${projectId}`,
        project_id: projectId,
        name: `Image ${index + 1}`,
        file_path: `/uploads/sample${index + 1}.jpg`,
        thumbnail_path: `/uploads/sample${index + 1}_thumb.jpg`,
        width: 800 + index * 100,
        height: 600 + index * 50,
        file_size: 120000 + index * 10000,
        mime_type: &quot;image/jpeg&quot;,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        status: &quot;processed&quot;
      }));

      res.setHeader('Content-Type', 'application/json');
      res.statusCode = 200;
      res.end(JSON.stringify(sampleImages));
    } else {
      res.statusCode = 404;
      res.setHeader('Content-Type', 'application/json');
      res.end(JSON.stringify({
        error: &quot;Project not found&quot;,
        message: &quot;The requested project does not exist&quot;
      }));
    }
  }
  // Default 404 handler
  else {
    res.statusCode = 404;
    res.setHeader('Content-Type', 'application/json');
    res.end(JSON.stringify({ error: &quot;Not found&quot; }));
  }
});

// Create uploads directory if it doesn't exist
const UPLOADS_DIR = path.join(__dirname, '../uploads');
if (!fs.existsSync(UPLOADS_DIR)) {
  fs.mkdirSync(UPLOADS_DIR, { recursive: true });
  console.log(`Created uploads directory: ${UPLOADS_DIR}`);
}

// Create mock-data directory if it doesn't exist
const MOCK_DATA_DIR = path.join(__dirname, 'mock-data');
if (!fs.existsSync(MOCK_DATA_DIR)) {
  fs.mkdirSync(MOCK_DATA_DIR, { recursive: true });
  console.log(`Created mock-data directory: ${MOCK_DATA_DIR}`);
  
  // Create empty projects.json file if it doesn't exist
  const PROJECTS_FILE = path.join(MOCK_DATA_DIR, 'projects.json');
  if (!fs.existsSync(PROJECTS_FILE)) {
    fs.writeFileSync(PROJECTS_FILE, JSON.stringify([]));
    console.log(`Created empty projects.json file`);
  }
}

// Add a simple Socket.IO mock implementation
// We'll use HTTP long polling instead of WebSockets since we don't have ws installed
const socketSessions = new Map();

// Generate a random session ID
const generateSessionId = () =&gt; {
  return Math.random().toString(36).substring(2, 15) +
         Math.random().toString(36).substring(2, 15);
};

// Handle Socket.IO HTTP requests
const handleSocketIORequest = (req, res) =&gt; {
  // Parse URL to get the specific Socket.IO endpoint
  const url = new URL(req.url, `http://${req.headers.host}`);
  const pathname = url.pathname;
  const query = Object.fromEntries(url.searchParams.entries());

  // Handle different Socket.IO endpoints
  if (pathname.includes('/socket.io/')) {
    // Handle handshake request (first request in Socket.IO connection)
    if (pathname === '/socket.io/' &amp;&amp; req.method === 'GET') {
      // Create a new session
      const sid = generateSessionId();
      const session = {
        id: sid,
        createdAt: Date.now(),
        lastActivity: Date.now(),
        messages: []
      };

      socketSessions.set(sid, session);

      // Send a valid Socket.IO handshake response
      res.setHeader('Content-Type', 'text/plain; charset=UTF-8');
      res.statusCode = 200;

      // Format: &lt;length&gt;:&lt;packet type&gt;&lt;data&gt;
      // 0 = open packet
      const handshakeData = JSON.stringify({
        sid: sid,
        upgrades: [&quot;websocket&quot;, &quot;polling&quot;],
        pingInterval: 25000,
        pingTimeout: 20000,
        maxPayload: 1000000
      });

      const packet = `0${handshakeData}`;
      const response = `${packet.length}:${packet}`;

      console.log(`Socket.IO handshake: created session ${sid}`);
      res.end(response);
    }
    // Handle polling requests (subsequent GET requests)
    else if (pathname.includes('/socket.io/') &amp;&amp; req.method === 'GET' &amp;&amp; pathname.includes('/polling/')) {
      // Extract session ID from URL
      const sidMatch = pathname.match(/\/socket\.io\/([^/]+)\/polling\//);
      const sid = sidMatch ? sidMatch[1] : null;

      if (sid &amp;&amp; socketSessions.has(sid)) {
        const session = socketSessions.get(sid);
        session.lastActivity = Date.now();

        // If there are messages to send, send them
        if (session.messages.length &gt; 0) {
          const messages = session.messages;
          session.messages = [];

          res.setHeader('Content-Type', 'text/plain; charset=UTF-8');
          res.statusCode = 200;
          res.end(messages.join(''));
        } else {
          // No messages, send empty response
          res.setHeader('Content-Type', 'text/plain; charset=UTF-8');
          res.statusCode = 200;
          res.end('');
        }
      } else {
        // Invalid session
        res.statusCode = 400;
        res.end('Invalid session');
      }
    }
    // Handle POST requests (client sending messages)
    else if (req.method === 'POST') {
      // Extract session ID from URL
      const sidMatch = pathname.match(/\/socket\.io\/([^/]+)\/polling\//);
      const sid = sidMatch ? sidMatch[1] : null;

      if (sid &amp;&amp; socketSessions.has(sid)) {
        const session = socketSessions.get(sid);
        session.lastActivity = Date.now();

        // Read request body
        let body = '';
        req.on('data', chunk =&gt; {
          body += chunk.toString();
        });

        req.on('end', () =&gt; {
          console.log(`Socket.IO message from client (${sid}):`, body);

          // Acknowledge the message
          res.setHeader('Content-Type', 'text/plain; charset=UTF-8');
          res.statusCode = 200;
          res.end('ok');
        });
      } else {
        // Invalid session
        res.statusCode = 400;
        res.end('Invalid session');
      }
    }
    else {
      // Unknown Socket.IO request
      res.setHeader('Content-Type', 'text/plain; charset=UTF-8');
      res.statusCode = 400;
      res.end('Invalid Socket.IO request');
    }
  } else {
    // Default Socket.IO response
    res.setHeader('Content-Type', 'application/json');
    res.statusCode = 200;
    res.end(JSON.stringify({
      status: &quot;ok&quot;,
      message: &quot;Socket.IO endpoint mock - real-time updates not available in simple server mode&quot;,
      timestamp: new Date().toISOString()
    }));
  }
};

// Handle WebSocket upgrade requests
server.on('upgrade', (request, socket, head) =&gt; {
  // We don't support WebSockets in this simple server
  // Just close the connection, the client will fall back to polling
  console.log('WebSocket upgrade request received, but not supported');
  socket.write('HTTP/1.1 400 Bad Request\r\n\r\n');
  socket.destroy();
});

// Start server
const PORT = process.env.PORT || 5001;
server.listen(PORT, '0.0.0.0', () =&gt; {
  console.log(`Server started on http://0.0.0.0:${PORT}`);
  console.log(`Environment: ${process.env.NODE_ENV || &quot;development&quot;}`);
  console.log(`Socket.IO endpoint available at /socket.io (with WebSocket support)`);
  
  // Log available authentication endpoints for diagnostics
  console.log(&quot;Available authentication endpoints:&quot;);
  console.log(&quot;- POST /api/auth/login (or /auth/login)&quot;);
  console.log(&quot;- POST /api/auth/register (or /auth/register)&quot;);
  console.log(&quot;- POST /api/auth/refresh (or /auth/refresh)&quot;);
  console.log(&quot;- POST /api/auth/logout (or /auth/logout)&quot;);
  console.log(&quot;- GET /api/users/me (or /users/me)&quot;);
});</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/simple-server-fixed.js (Line 1:1 - Line 808:2), packages/backend/src/simple-server.js (Line 1:1 - Line 808:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn162" onclick="toggleCodeBlock('cloneGroup162', 'expandBtn162', 'collapseBtn162')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn162" onclick="toggleCodeBlock('cloneGroup162', 'expandBtn162', 'collapseBtn162')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup162"><code class="language-javascript text-sm text-gray-800">// Simple HTTP server without dependencies
const http = require('http');
const fs = require('fs');
const path = require('path');

// Create HTTP server
const server = http.createServer((req, res) =&gt; {
  console.log(`${req.method} ${req.url}`);

  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.statusCode = 204;
    res.end();
    return;
  }

  // Parse URL
  const url = new URL(req.url, `http://${req.headers.host}`);
  const pathname = url.pathname;

  // Handle API endpoints
  if (pathname === '/api/health') {
    res.setHeader('Content-Type', 'application/json');
    res.statusCode = 200;
    res.end(JSON.stringify({
      status: &quot;OK&quot;,
      timestamp: new Date().toISOString(),
      service: &quot;backend-simple&quot;
    }));
  }
  else if (pathname === '/api/status') {
    res.setHeader('Content-Type', 'application/json');
    res.statusCode = 200;
    res.end(JSON.stringify({
      status: &quot;running&quot;,
      version: &quot;1.0.0&quot;,
      environment: process.env.NODE_ENV || &quot;development&quot;,
      uptime: process.uptime()
    }));
  }
  else if (pathname === '/api/metrics/performance') {
    res.setHeader('Content-Type', 'application/json');
    res.statusCode = 200;
    res.end(JSON.stringify({ success: true }));
  }
  // Support multiple paths for login (compatibility with standard Express router)
  else if (pathname === '/api/auth/login' || pathname === '/auth/login') {
    // Parse request body
    let body = '';
    req.on('data', chunk =&gt; {
      body += chunk.toString();
    });

    req.on('end', () =&gt; {
      try {
        const data = JSON.parse(body);
        console.log('Login attempt:', data.email);

        // Simple mock authentication
        if (data.email &amp;&amp; data.password) {
          res.setHeader('Content-Type', 'application/json');
          res.statusCode = 200;
          res.end(JSON.stringify({
            user: {
              id: &quot;user-12bprusek-gym-nymburk-cz&quot;,
              email: data.email,
              name: &quot;Michal Průšek&quot;,
              created_at: new Date().toISOString(),
              preferred_language: &quot;en&quot;,
              theme_preference: &quot;dark&quot;
            },
            accessToken: &quot;mock-access-token-&quot; + Date.now(),
            refreshToken: &quot;mock-refresh-token-&quot; + Date.now(),
            tokenType: &quot;Bearer&quot;
          }));
        } else {
          res.statusCode = 401;
          res.setHeader('Content-Type', 'application/json');
          res.end(JSON.stringify({
            message: &quot;Invalid email or password&quot;
          }));
        }
      } catch (error) {
        res.statusCode = 400;
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify({
          error: 'Invalid request body',
          message: error.message
        }));
      }
    });
  }
  // Handle register endpoint - support multiple paths for compatibility
  else if (pathname === '/api/auth/register' || pathname === '/auth/register') {
    // Parse request body
    let body = '';
    req.on('data', chunk =&gt; {
      body += chunk.toString();
    });

    req.on('end', () =&gt; {
      try {
        const data = JSON.parse(body);
        console.log('Registration attempt:', data.email);

        // Simple mock registration
        if (data.email &amp;&amp; data.password) {
          res.setHeader('Content-Type', 'application/json');
          res.statusCode = 201;
          res.end(JSON.stringify({
            user: {
              id: &quot;user-&quot; + data.email.replace(/[^a-zA-Z0-9]/g, '-'),
              email: data.email,
              name: data.name || data.email.split('@')[0],
              created_at: new Date().toISOString()
            },
            accessToken: &quot;mock-access-token-&quot; + Date.now(),
            refreshToken: &quot;mock-refresh-token-&quot; + Date.now(),
            tokenType: &quot;Bearer&quot;
          }));
        } else {
          res.statusCode = 400;
          res.setHeader('Content-Type', 'application/json');
          res.end(JSON.stringify({
            message: &quot;Email and password are required&quot;
          }));
        }
      } catch (error) {
        res.statusCode = 400;
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify({
          error: 'Invalid request body',
          message: error.message
        }));
      }
    });
  }
  // Handle refresh token endpoint - support multiple paths for compatibility
  else if (pathname === '/api/auth/refresh' || pathname === '/auth/refresh') {
    // Parse request body
    let body = '';
    req.on('data', chunk =&gt; {
      body += chunk.toString();
    });

    req.on('end', () =&gt; {
      try {
        const data = JSON.parse(body);
        console.log('Token refresh attempt');

        // Simple mock token refresh
        if (data.refreshToken) {
          res.setHeader('Content-Type', 'application/json');
          res.statusCode = 200;
          res.end(JSON.stringify({
            accessToken: &quot;mock-access-token-&quot; + Date.now(),
            refreshToken: &quot;mock-refresh-token-&quot; + Date.now(),
            tokenType: &quot;Bearer&quot;
          }));
        } else {
          res.statusCode = 400;
          res.setHeader('Content-Type', 'application/json');
          res.end(JSON.stringify({
            message: &quot;Refresh token is required&quot;
          }));
        }
      } catch (error) {
        res.statusCode = 400;
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify({
          error: 'Invalid request body',
          message: error.message
        }));
      }
    });
  }
  // Handle logout endpoint - support multiple paths for compatibility
  else if (pathname === '/api/auth/logout' || pathname === '/auth/logout') {
    // Parse request body
    let body = '';
    req.on('data', chunk =&gt; {
      body += chunk.toString();
    });

    req.on('end', () =&gt; {
      try {
        const data = JSON.parse(body);
        console.log('Logout attempt');

        // Simple mock logout
        res.setHeader('Content-Type', 'application/json');
        res.statusCode = 200;
        res.end(JSON.stringify({
          message: &quot;Logged out successfully&quot;
        }));
      } catch (error) {
        res.statusCode = 400;
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify({
          error: 'Invalid request body',
          message: error.message
        }));
      }
    });
  }
  else if (pathname === '/api/users/me' || pathname === '/users/me') {
    res.setHeader('Content-Type', 'application/json');
    res.statusCode = 200;
    res.end(JSON.stringify({
      id: &quot;user-12bprusek-gym-nymburk-cz&quot;,
      email: &quot;<EMAIL>&quot;,
      name: &quot;Michal Průšek&quot;,
      role: &quot;admin&quot;,
      created_at: new Date().toISOString(),
      profile: {
        preferred_language: &quot;en&quot;,
        theme_preference: &quot;dark&quot;
      }
    }));
  }
  else if (pathname === '/api/users/me/statistics' || pathname === '/api/users/me/stats') {
    res.setHeader('Content-Type', 'application/json');
    res.statusCode = 200;
    res.end(JSON.stringify({
      projects_count: 5,
      images_count: 120,
      segmentations_count: 98,
      last_login: new Date().toISOString()
    }));
  }
  else if (pathname === '/api/projects' &amp;&amp; req.method === 'GET') {
    // Parse query parameters
    const limit = parseInt(url.searchParams.get('limit')) || 10;
    const offset = parseInt(url.searchParams.get('offset')) || 0;

    // Load projects from file
    let projects = [];
    try {
      const fs = require('fs');
      const path = require('path');
      const projectsPath = path.join(__dirname, 'mock-data', 'projects.json');
      if (fs.existsSync(projectsPath)) {
        try {
          projects = JSON.parse(fs.readFileSync(projectsPath, 'utf8'));
          console.log(`Loaded ${projects.length} projects`);
        } catch (parseError) {
          console.error('Error parsing projects file:', parseError);
        }
      } else {
        console.log('Projects file not found, creating empty file');
        // Create the directory if it doesn't exist
        const mockDataDir = path.join(__dirname, 'mock-data');
        if (!fs.existsSync(mockDataDir)) {
          fs.mkdirSync(mockDataDir, { recursive: true });
        }
        // Create an empty projects file
        fs.writeFileSync(projectsPath, JSON.stringify([]));
      }
    } catch (error) {
      console.error('Error loading projects:', error);
    }

    res.setHeader('Content-Type', 'application/json');
    res.statusCode = 200;
    res.end(JSON.stringify({
      projects,
      total: projects.length,
      limit,
      offset
    }));
  }
  // Handle specific project details
  else if (pathname.match(/^\/api\/projects\/project-/) &amp;&amp; req.method === 'GET') {
    const projectId = pathname.split('/').pop();

    // Try to find project in data
    let project = null;
    try {
      const fs = require('fs');
      const path = require('path');
      const projectsPath = path.join(__dirname, 'mock-data', 'projects.json');
      if (fs.existsSync(projectsPath)) {
        try {
          const projects = JSON.parse(fs.readFileSync(projectsPath, 'utf8'));
          project = projects.find(p =&gt; p.id === projectId);
          console.log(`Looking for project ${projectId}, found: ${project ? 'yes' : 'no'}`);
        } catch (parseError) {
          console.error('Error parsing projects file:', parseError);
        }
      }
    } catch (error) {
      console.error('Error finding project:', error);
    }

    if (project) {
      res.setHeader('Content-Type', 'application/json');
      res.statusCode = 200;
      res.end(JSON.stringify(project));
    } else {
      res.setHeader('Content-Type', 'application/json');
      res.statusCode = 404;
      res.end(JSON.stringify({
        error: &quot;Project not found&quot;,
        message: &quot;The requested project does not exist&quot;
      }));
    }
  }
  // Handle queue status
  else if (pathname.match(/^\/api\/queue-status\/project-\d+$/)) {
    const projectId = pathname.split('/').pop();

    res.setHeader('Content-Type', 'application/json');
    res.statusCode = 200;
    res.end(JSON.stringify({
      project_id: projectId,
      total_images: 15,
      processed_images: 12,
      pending_images: 3,
      failed_images: 0,
      status: &quot;processing&quot;,
      last_updated: new Date().toISOString()
    }));
  }
  // Handle project images
  else if (pathname.match(/^\/api\/projects\/project-\d+\/images$/)) {
    const projectId = pathname.split('/')[3];

    // Create mock images for project
    const mockImages = [
      {
        id: `image-1-${projectId}`,
        project_id: projectId,
        name: &quot;Sample Image 1&quot;,
        file_path: &quot;/uploads/sample1.jpg&quot;,
        thumbnail_path: &quot;/uploads/sample1_thumb.jpg&quot;,
        width: 800,
        height: 600,
        file_size: 120000,
        mime_type: &quot;image/jpeg&quot;,
        created_at: new Date().toISOString(),
        status: &quot;processed&quot;
      },
      {
        id: `image-2-${projectId}`,
        project_id: projectId,
        name: &quot;Sample Image 2&quot;,
        file_path: &quot;/uploads/sample2.jpg&quot;,
        thumbnail_path: &quot;/uploads/sample2_thumb.jpg&quot;,
        width: 1024,
        height: 768,
        file_size: 150000,
        mime_type: &quot;image/jpeg&quot;,
        created_at: new Date().toISOString(),
        status: &quot;processed&quot;
      },
      {
        id: `image-3-${projectId}`,
        project_id: projectId,
        name: &quot;Sample Image 3&quot;,
        file_path: &quot;/uploads/sample3.jpg&quot;,
        thumbnail_path: &quot;/uploads/sample3_thumb.jpg&quot;,
        width: 1280,
        height: 720,
        file_size: 180000,
        mime_type: &quot;image/jpeg&quot;,
        created_at: new Date().toISOString(),
        status: &quot;processed&quot;
      }
    ];

    res.setHeader('Content-Type', 'application/json');
    res.statusCode = 200;
    res.end(JSON.stringify(mockImages));
  }
  // Handle API POST to /api/projects
  else if (pathname === '/api/projects' &amp;&amp; req.method === 'POST') {
    // Parse request body
    let body = '';
    req.on('data', chunk =&gt; {
      body += chunk.toString();
    });

    req.on('end', () =&gt; {
      try {
        const data = JSON.parse(body);
        console.log('Creating project with data:', data);

        const newProject = {
          id: `project-${Date.now()}`,
          name: data.name,
          description: data.description || '',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          user_id: &quot;user-12bprusek-gym-nymburk-cz&quot;,
          status: &quot;active&quot;,
          image_count: 0
        };

        // Try to save to mock projects file
        try {
          const fs = require('fs');
          const path = require('path');
          const mockDataDir = path.join(__dirname, 'mock-data');
          const mockProjectsPath = path.join(mockDataDir, 'projects.json');
          let projects = [];

          if (fs.existsSync(mockProjectsPath)) {
            try {
              projects = JSON.parse(fs.readFileSync(mockProjectsPath, 'utf8'));
              console.log(`Loaded ${projects.length} existing projects`);
            } catch (parseError) {
              console.error('Error parsing projects file, starting with empty projects:', parseError);
              projects = [];
            }
          } else {
            console.log('Projects file not found, creating new file');
            fs.mkdirSync(path.dirname(mockProjectsPath), { recursive: true });
          }

          projects.push(newProject);
          fs.writeFileSync(mockProjectsPath, JSON.stringify(projects, null, 2));
          console.log(`Added new project to data: ${newProject.id}`);
        } catch (error) {
          console.error('Error saving project to data:', error);
        }

        res.setHeader('Content-Type', 'application/json');
        res.statusCode = 201;
        res.end(JSON.stringify(newProject));
      } catch (error) {
        res.statusCode = 400;
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify({
          error: 'Invalid request body',
          message: error.message
        }));
      }
    });
  }
  // Handle DELETE project
  else if (pathname.match(/^\/api\/projects\/project-/) &amp;&amp; req.method === 'DELETE') {
    const projectId = pathname.split('/').pop();
    console.log(`Deleting project with ID: ${projectId}`);

    try {
      const fs = require('fs');
      const path = require('path');
      const projectsPath = path.join(__dirname, 'mock-data', 'projects.json');

      if (fs.existsSync(projectsPath)) {
        try {
          let projects = JSON.parse(fs.readFileSync(projectsPath, 'utf8'));
          const initialLength = projects.length;

          // Find the project index
          const projectIndex = projects.findIndex(p =&gt; p.id === projectId);

          if (projectIndex !== -1) {
            // Remove the project
            projects.splice(projectIndex, 1);
            fs.writeFileSync(projectsPath, JSON.stringify(projects, null, 2));
            console.log(`Project ${projectId} deleted. Projects remaining: ${projects.length}`);

            // Return 204 status code (success with no content) to match the real API
            res.statusCode = 204;
            res.end();
          } else {
            console.error(`Project ${projectId} not found for deletion`);
            res.statusCode = 404;
            res.setHeader('Content-Type', 'application/json');
            res.end(JSON.stringify({
              error: &quot;Project not found&quot;,
              message: &quot;The requested project does not exist or has already been deleted&quot;
            }));
          }
        } catch (parseError) {
          console.error('Error parsing projects file for deletion:', parseError);
          res.statusCode = 500;
          res.setHeader('Content-Type', 'application/json');
          res.end(JSON.stringify({
            error: &quot;Internal server error&quot;,
            message: &quot;Error reading project data&quot;
          }));
        }
      } else {
        // Create the directory if it doesn't exist
        const mockDataDir = path.join(__dirname, 'mock-data');
        if (!fs.existsSync(mockDataDir)) {
          fs.mkdirSync(mockDataDir, { recursive: true });
        }
        // Create an empty projects file
        fs.writeFileSync(projectsPath, JSON.stringify([]));

        res.statusCode = 404;
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify({
          error: &quot;Project not found&quot;,
          message: &quot;No projects data available&quot;
        }));
      }
    } catch (error) {
      console.error('Error deleting project:', error);
      res.statusCode = 500;
      res.setHeader('Content-Type', 'application/json');
      res.end(JSON.stringify({
        error: &quot;Internal server error&quot;,
        message: error.message
      }));
    }
  }
  // Handle batch segmentation endpoint
  else if (pathname === '/images/segmentation/trigger-batch' || pathname === '/api/images/segmentation/trigger-batch') {
    // Parse request body for batch segmentation
    let body = '';
    req.on('data', chunk =&gt; {
      body += chunk.toString();
    });

    req.on('end', () =&gt; {
      try {
        const data = JSON.parse(body);
        console.log('Received batch segmentation request:', data);

        res.setHeader('Content-Type', 'application/json');
        res.statusCode = 200;
        res.end(JSON.stringify({
          success: true,
          message: 'Batch segmentation triggered successfully',
          jobId: 'mock-job-' + Date.now(),
          imageIds: data.imageIds || [],
          timestamp: new Date().toISOString()
        }));
      } catch (error) {
        res.statusCode = 400;
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify({
          error: 'Invalid request body',
          message: error.message
        }));
      }
    });
  }
  // Handle Socket.IO endpoint
  else if (pathname === '/socket.io' || pathname.startsWith('/socket.io/')) {
    handleSocketIORequest(req, res);
  }
  // Handle static files from uploads directory
  else if (pathname.startsWith('/uploads/')) {
    const filePath = path.join(__dirname, '..', pathname);

    // Check if file exists
    fs.access(filePath, fs.constants.F_OK, (err) =&gt; {
      if (err) {
        res.statusCode = 404;
        res.end(JSON.stringify({ error: &quot;File not found&quot; }));
        return;
      }

      // Determine content type
      let contentType = 'application/octet-stream';
      if (filePath.endsWith('.jpg') || filePath.endsWith('.jpeg')) {
        contentType = 'image/jpeg';
      } else if (filePath.endsWith('.png')) {
        contentType = 'image/png';
      } else if (filePath.endsWith('.json')) {
        contentType = 'application/json';
      }

      // Stream the file
      res.setHeader('Content-Type', contentType);
      fs.createReadStream(filePath).pipe(res);
    });
  }
  // Add support for project images
  else if (pathname.match(/^\/api\/projects\/project-.*\/images$/)) {
    const projectId = pathname.split('/')[3]; // Extract project ID from URL

    // Try to find project
    let project = null;
    try {
      const fs = require('fs');
      const path = require('path');
      const projectsPath = path.join(__dirname, 'mock-data', 'projects.json');
      if (fs.existsSync(projectsPath)) {
        const projects = JSON.parse(fs.readFileSync(projectsPath, 'utf8'));
        project = projects.find(p =&gt; p.id === projectId);
      }
    } catch (error) {
      console.error('Error finding project for images:', error);
    }

    if (project) {
      // Return sample images for the project
      const sampleImages = Array.from({ length: project.image_count || 0 }).map((_, index) =&gt; ({
        id: `image-${index + 1}-${projectId}`,
        project_id: projectId,
        name: `Image ${index + 1}`,
        file_path: `/uploads/sample${index + 1}.jpg`,
        thumbnail_path: `/uploads/sample${index + 1}_thumb.jpg`,
        width: 800 + index * 100,
        height: 600 + index * 50,
        file_size: 120000 + index * 10000,
        mime_type: &quot;image/jpeg&quot;,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        status: &quot;processed&quot;
      }));

      res.setHeader('Content-Type', 'application/json');
      res.statusCode = 200;
      res.end(JSON.stringify(sampleImages));
    } else {
      res.statusCode = 404;
      res.setHeader('Content-Type', 'application/json');
      res.end(JSON.stringify({
        error: &quot;Project not found&quot;,
        message: &quot;The requested project does not exist&quot;
      }));
    }
  }
  // Default 404 handler
  else {
    res.statusCode = 404;
    res.setHeader('Content-Type', 'application/json');
    res.end(JSON.stringify({ error: &quot;Not found&quot; }));
  }
});

// Create uploads directory if it doesn't exist
const UPLOADS_DIR = path.join(__dirname, '../uploads');
if (!fs.existsSync(UPLOADS_DIR)) {
  fs.mkdirSync(UPLOADS_DIR, { recursive: true });
  console.log(`Created uploads directory: ${UPLOADS_DIR}`);
}

// Create mock-data directory if it doesn't exist
const MOCK_DATA_DIR = path.join(__dirname, 'mock-data');
if (!fs.existsSync(MOCK_DATA_DIR)) {
  fs.mkdirSync(MOCK_DATA_DIR, { recursive: true });
  console.log(`Created mock-data directory: ${MOCK_DATA_DIR}`);
  
  // Create empty projects.json file if it doesn't exist
  const PROJECTS_FILE = path.join(MOCK_DATA_DIR, 'projects.json');
  if (!fs.existsSync(PROJECTS_FILE)) {
    fs.writeFileSync(PROJECTS_FILE, JSON.stringify([]));
    console.log(`Created empty projects.json file`);
  }
}

// Add a simple Socket.IO mock implementation
// We'll use HTTP long polling instead of WebSockets since we don't have ws installed
const socketSessions = new Map();

// Generate a random session ID
const generateSessionId = () =&gt; {
  return Math.random().toString(36).substring(2, 15) +
         Math.random().toString(36).substring(2, 15);
};

// Handle Socket.IO HTTP requests
const handleSocketIORequest = (req, res) =&gt; {
  // Parse URL to get the specific Socket.IO endpoint
  const url = new URL(req.url, `http://${req.headers.host}`);
  const pathname = url.pathname;
  const query = Object.fromEntries(url.searchParams.entries());

  // Handle different Socket.IO endpoints
  if (pathname.includes('/socket.io/')) {
    // Handle handshake request (first request in Socket.IO connection)
    if (pathname === '/socket.io/' &amp;&amp; req.method === 'GET') {
      // Create a new session
      const sid = generateSessionId();
      const session = {
        id: sid,
        createdAt: Date.now(),
        lastActivity: Date.now(),
        messages: []
      };

      socketSessions.set(sid, session);

      // Send a valid Socket.IO handshake response
      res.setHeader('Content-Type', 'text/plain; charset=UTF-8');
      res.statusCode = 200;

      // Format: &lt;length&gt;:&lt;packet type&gt;&lt;data&gt;
      // 0 = open packet
      const handshakeData = JSON.stringify({
        sid: sid,
        upgrades: [&quot;websocket&quot;, &quot;polling&quot;],
        pingInterval: 25000,
        pingTimeout: 20000,
        maxPayload: 1000000
      });

      const packet = `0${handshakeData}`;
      const response = `${packet.length}:${packet}`;

      console.log(`Socket.IO handshake: created session ${sid}`);
      res.end(response);
    }
    // Handle polling requests (subsequent GET requests)
    else if (pathname.includes('/socket.io/') &amp;&amp; req.method === 'GET' &amp;&amp; pathname.includes('/polling/')) {
      // Extract session ID from URL
      const sidMatch = pathname.match(/\/socket\.io\/([^/]+)\/polling\//);
      const sid = sidMatch ? sidMatch[1] : null;

      if (sid &amp;&amp; socketSessions.has(sid)) {
        const session = socketSessions.get(sid);
        session.lastActivity = Date.now();

        // If there are messages to send, send them
        if (session.messages.length &gt; 0) {
          const messages = session.messages;
          session.messages = [];

          res.setHeader('Content-Type', 'text/plain; charset=UTF-8');
          res.statusCode = 200;
          res.end(messages.join(''));
        } else {
          // No messages, send empty response
          res.setHeader('Content-Type', 'text/plain; charset=UTF-8');
          res.statusCode = 200;
          res.end('');
        }
      } else {
        // Invalid session
        res.statusCode = 400;
        res.end('Invalid session');
      }
    }
    // Handle POST requests (client sending messages)
    else if (req.method === 'POST') {
      // Extract session ID from URL
      const sidMatch = pathname.match(/\/socket\.io\/([^/]+)\/polling\//);
      const sid = sidMatch ? sidMatch[1] : null;

      if (sid &amp;&amp; socketSessions.has(sid)) {
        const session = socketSessions.get(sid);
        session.lastActivity = Date.now();

        // Read request body
        let body = '';
        req.on('data', chunk =&gt; {
          body += chunk.toString();
        });

        req.on('end', () =&gt; {
          console.log(`Socket.IO message from client (${sid}):`, body);

          // Acknowledge the message
          res.setHeader('Content-Type', 'text/plain; charset=UTF-8');
          res.statusCode = 200;
          res.end('ok');
        });
      } else {
        // Invalid session
        res.statusCode = 400;
        res.end('Invalid session');
      }
    }
    else {
      // Unknown Socket.IO request
      res.setHeader('Content-Type', 'text/plain; charset=UTF-8');
      res.statusCode = 400;
      res.end('Invalid Socket.IO request');
    }
  } else {
    // Default Socket.IO response
    res.setHeader('Content-Type', 'application/json');
    res.statusCode = 200;
    res.end(JSON.stringify({
      status: &quot;ok&quot;,
      message: &quot;Socket.IO endpoint mock - real-time updates not available in simple server mode&quot;,
      timestamp: new Date().toISOString()
    }));
  }
};

// Handle WebSocket upgrade requests
server.on('upgrade', (request, socket, head) =&gt; {
  // We don't support WebSockets in this simple server
  // Just close the connection, the client will fall back to polling
  console.log('WebSocket upgrade request received, but not supported');
  socket.write('HTTP/1.1 400 Bad Request\r\n\r\n');
  socket.destroy();
});

// Start server
const PORT = process.env.PORT || 5001;
server.listen(PORT, '0.0.0.0', () =&gt; {
  console.log(`Server started on http://0.0.0.0:${PORT}`);
  console.log(`Environment: ${process.env.NODE_ENV || &quot;development&quot;}`);
  console.log(`Socket.IO endpoint available at /socket.io (with WebSocket support)`);
  
  // Log available authentication endpoints for diagnostics
  console.log(&quot;Available authentication endpoints:&quot;);
  console.log(&quot;- POST /api/auth/login (or /auth/login)&quot;);
  console.log(&quot;- POST /api/auth/register (or /auth/register)&quot;);
  console.log(&quot;- POST /api/auth/refresh (or /auth/refresh)&quot;);
  console.log(&quot;- POST /api/auth/logout (or /auth/logout)&quot;);
  console.log(&quot;- GET /api/users/me (or /users/me)&quot;);
});</code></pre></div><div class="py-4"><p class="text-gray-600">packages/backend/src/delete-handler.js (Line 1:3 - Line 71:2), packages/backend/src/simple-server.js (Line 445:3 - Line 515:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn163" onclick="toggleCodeBlock('cloneGroup163', 'expandBtn163', 'collapseBtn163')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn163" onclick="toggleCodeBlock('cloneGroup163', 'expandBtn163', 'collapseBtn163')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup163"><code class="language-javascript text-sm text-gray-800">// Handle DELETE project
  else if (pathname.match(/^\/api\/projects\/project-/) &amp;&amp; req.method === 'DELETE') {
    const projectId = pathname.split('/').pop();
    console.log(`Deleting project with ID: ${projectId}`);

    try {
      const fs = require('fs');
      const path = require('path');
      const projectsPath = path.join(__dirname, 'mock-data', 'projects.json');

      if (fs.existsSync(projectsPath)) {
        try {
          let projects = JSON.parse(fs.readFileSync(projectsPath, 'utf8'));
          const initialLength = projects.length;

          // Find the project index
          const projectIndex = projects.findIndex(p =&gt; p.id === projectId);

          if (projectIndex !== -1) {
            // Remove the project
            projects.splice(projectIndex, 1);
            fs.writeFileSync(projectsPath, JSON.stringify(projects, null, 2));
            console.log(`Project ${projectId} deleted. Projects remaining: ${projects.length}`);

            // Return 204 status code (success with no content) to match the real API
            res.statusCode = 204;
            res.end();
          } else {
            console.error(`Project ${projectId} not found for deletion`);
            res.statusCode = 404;
            res.setHeader('Content-Type', 'application/json');
            res.end(JSON.stringify({
              error: &quot;Project not found&quot;,
              message: &quot;The requested project does not exist or has already been deleted&quot;
            }));
          }
        } catch (parseError) {
          console.error('Error parsing projects file for deletion:', parseError);
          res.statusCode = 500;
          res.setHeader('Content-Type', 'application/json');
          res.end(JSON.stringify({
            error: &quot;Internal server error&quot;,
            message: &quot;Error reading project data&quot;
          }));
        }
      } else {
        // Create the directory if it doesn't exist
        const mockDataDir = path.join(__dirname, 'mock-data');
        if (!fs.existsSync(mockDataDir)) {
          fs.mkdirSync(mockDataDir, { recursive: true });
        }
        // Create an empty projects file
        fs.writeFileSync(projectsPath, JSON.stringify([]));

        res.statusCode = 404;
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify({
          error: &quot;Project not found&quot;,
          message: &quot;No projects data available&quot;
        }));
      }
    } catch (error) {
      console.error('Error deleting project:', error);
      res.statusCode = 500;
      res.setHeader('Content-Type', 'application/json');
      res.end(JSON.stringify({
        error: &quot;Internal server error&quot;,
        message: error.message
      }));
    }
  }</code></pre></div><!-- Add more clone groups for .txt format as needed         // Add more clone groups for .txt format as needed--></div><a name="css-clones"></a><h2 class="text-2xl font-semibold text-gray-700 mb-4">css</h2><div class="divide-y divide-gray-200 border-b-2"><!-- Add more clone groups for .txt format as needed         // Add more clone groups for .txt format as needed--></div></section><!-- Add more sections for other formats and clone groups as needed--></main><footer class="bg-white shadow mt-8 py-4"><div class="container mx-auto px-4 text-center"><p class="text-sm text-gray-600">This report is generated by jscpd, an open-source copy/paste detector.</p><p class="text-sm text-gray-600">jscpd is licensed under the MIT License.</p><a class="text-blue-500 text-sm" href="https://github.com/kucherenko/jscpd" target="_blank" rel="noopener noreferrer">View jscpd on GitHub</a></div></footer><script src="js/prism.js"></script><script>function toggleCodeBlock(codeBlockId, expandBtnId, collapseBtnId) {
  const codeBlock = document.getElementById(codeBlockId);
  const expandBtn = document.getElementById(expandBtnId);
  const collapseBtn = document.getElementById(collapseBtnId);

  codeBlock.classList.toggle('hidden');
  expandBtn.classList.toggle('hidden');
  collapseBtn.classList.toggle('hidden');
}</script></body></html>