user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log notice;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    keepalive_timeout  65;
    gzip  on;
    
    # Define custom MIME types
    types {
        text/css                css;
        application/javascript  js mjs;
    }

    # HTTP server - redirect to HTTPS
    server {
        listen 80;
        server_name spherosegapp.utia.cas.cz localhost;
        return 301 https://$host$request_uri;
    }

    # HTTPS server
    server {
        listen 443 ssl;
        server_name spherosegapp.utia.cas.cz localhost;
        
        # SSL configuration
        ssl_certificate /etc/nginx/ssl/server.crt;
        ssl_certificate_key /etc/nginx/ssl/server.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        
        # CORS settings
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
        
        # Special handling for OPTIONS requests (CORS preflight)
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain charset=UTF-8';
            add_header 'Content-Length' 0;
            return 204;
        }
        
        # Handle CSS files explicitly
        location ~* \.css$ {
            proxy_pass http://frontend-dev:3000;
            proxy_set_header Host $host;
            add_header Content-Type "text/css";
            expires 30d;
        }
        
        # Handle JS files explicitly
        location ~* \.js$ {
            proxy_pass http://frontend-dev:3000;
            proxy_set_header Host $host;
            add_header Content-Type "application/javascript";
            expires 30d;
        }
        
        # Vite dependency files in node_modules
        location /node_modules/ {
            proxy_pass http://frontend-dev:3000;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
        }
        
        # Source files in /src
        location /src/ {
            proxy_pass http://frontend-dev:3000;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
        }
        
        # WebSocket for HMR
        location /@hmr {
            proxy_pass http://frontend-dev:3000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
        }
        
        # API proxy
        location /api/ {
            proxy_pass http://backend:5001/api/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # Assets proxy
        location /assets/ {
            proxy_pass http://assets:80/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        # Default location - proxy everything else to Vite dev server
        location / {
            proxy_pass http://frontend-dev:3000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }
    }
}