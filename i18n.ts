import i18n from "/node_modules/.vite/deps/i18next.js?v=d77b29d6";
import { initReactI18next } from "/node_modules/.vite/deps/react-i18next.js?v=d77b29d6";
import enTranslations from "/src/translations/en.ts?t=1747068600391";
import csTranslations from "/src/translations/cs.ts?t=1747068695762";
import deTranslations from "/src/translations/de.ts";
import esTranslations from "/src/translations/es.ts";
import frTranslations from "/src/translations/fr.ts";
import zhTranslations from "/src/translations/zh.ts";
const projectNoImagesTranslations = {
  title: "No Images Yet",
  description: "This project doesn't have any images yet. Upload images to get started with segmentation.",
  uploadButton: "Upload Images"
};
if (!enTranslations) {
  console.warn("English translations not found, creating default object");
  window.enTranslations = {};
}
if (!enTranslations.project) {
  enTranslations.project = {};
}
enTranslations.project.noImages = projectNoImagesTranslations;
enTranslations.project.errorLoading = "Error loading project";
i18n.use(initReactI18next).init({
  resources: {
    en: { translation: enTranslations },
    cs: { translation: csTranslations },
    de: { translation: deTranslations },
    es: { translation: esTranslations },
    fr: { translation: frTranslations },
    zh: { translation: zhTranslations }
  },
  lng: "en",
  // Default language
  fallbackLng: "en",
  interpolation: {
    escapeValue: false
    // React already escapes values
  },
  react: {
    useSuspense: false
    // Disable suspense to avoid issues
  }
});
i18n.addResourceBundle(
  "en",
  "translation",
  {
    project: {
      noImages: projectNoImagesTranslations,
      errorLoading: "Error loading project"
    }
  },
  true,
  true
);
export default i18n;

//# sourceMappingURL=data:application/json;base64,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